/**
 * StatusIndicator Atom - Electrical System Status Display
 * 
 * Atomic design status indicator component providing visual status feedback
 * for electrical systems, components, and operations with engineering-grade quality.
 * 
 * Features:
 * - Atomic design principles (single responsibility)
 * - Electrical engineering status variants
 * - WCAG 2.1 AA accessibility compliance
 * - TypeScript strict mode compliance
 * - Performance optimized with minimal footprint
 * - Consistent design system integration
 * - Professional electrical design standards
 */

import React from "react"
import type { VariantProps } from "class-variance-authority"
import { cva } from "class-variance-authority"
import { 
  CheckCircle2, 
  AlertTriangle, 
  XCircle, 
  Clock, 
  Zap, 
  AlertCircle, 
  Pause, 
  Power, 
  Settings,
  Activity,
  type LucideIcon
} from "lucide-react"

import { cn } from "@/lib/utils"

// Status indicator variants using CVA for consistent styling
const statusIndicatorVariants = cva(
  "inline-flex items-center gap-1.5 font-medium transition-all duration-200",
  {
    variants: {
      variant: {
        // System Status Variants
        operational: "text-green-700 dark:text-green-400",
        warning: "text-amber-700 dark:text-amber-400", 
        critical: "text-red-700 dark:text-red-400",
        offline: "text-gray-600 dark:text-gray-400",
        maintenance: "text-blue-700 dark:text-blue-400",
        
        // Electrical Status Variants
        energized: "text-emerald-600 dark:text-emerald-400",
        deenergized: "text-slate-600 dark:text-slate-400",
        fault: "text-red-600 dark:text-red-400",
        overload: "text-orange-600 dark:text-orange-400",
        undervoltage: "text-yellow-600 dark:text-yellow-400",
        
        // Process Status Variants
        active: "text-green-600 dark:text-green-400",
        inactive: "text-gray-500 dark:text-gray-400",
        pending: "text-blue-600 dark:text-blue-400",
        processing: "text-purple-600 dark:text-purple-400",
        completed: "text-green-600 dark:text-green-500",
        failed: "text-red-600 dark:text-red-400",
      },
      size: {
        xs: "text-xs",
        sm: "text-sm",
        md: "text-sm",
        lg: "text-base",
        xl: "text-lg",
      },
      appearance: {
        default: "",
        badge: "px-2 py-1 rounded-full border",
        dot: "items-center",
        pill: "px-3 py-1.5 rounded-full bg-opacity-10 border border-opacity-20",
      },
    },
    compoundVariants: [
      // Badge style variants
      {
        variant: "operational",
        appearance: "badge",
        className: "bg-green-50 border-green-200 dark:bg-green-950/50 dark:border-green-800",
      },
      {
        variant: "warning", 
        appearance: "badge",
        className: "bg-amber-50 border-amber-200 dark:bg-amber-950/50 dark:border-amber-800",
      },
      {
        variant: "critical",
        appearance: "badge", 
        className: "bg-red-50 border-red-200 dark:bg-red-950/50 dark:border-red-800",
      },
      {
        variant: "energized",
        appearance: "badge",
        className: "bg-emerald-50 border-emerald-200 dark:bg-emerald-950/50 dark:border-emerald-800",
      },
      {
        variant: "fault",
        appearance: "badge",
        className: "bg-red-50 border-red-200 dark:bg-red-950/50 dark:border-red-800",
      },
      // Pill style variants  
      {
        variant: "operational",
        appearance: "pill",
        className: "bg-green-500 border-green-500",
      },
      {
        variant: "warning",
        appearance: "pill", 
        className: "bg-amber-500 border-amber-500",
      },
      {
        variant: "critical",
        appearance: "pill",
        className: "bg-red-500 border-red-500", 
      },
    ],
    defaultVariants: {
      variant: "operational",
      size: "md",
      appearance: "default",
    },
  }
)

// Icon size configuration based on text size
const iconSizeConfig = {
  xs: "h-3 w-3",
  sm: "h-4 w-4", 
  md: "h-4 w-4",
  lg: "h-5 w-5",
  xl: "h-6 w-6",
} as const

// Status to icon mapping for electrical engineering context
const statusIconMap: Record<string, LucideIcon> = {
  // System Status Icons
  operational: CheckCircle2,
  warning: AlertTriangle,
  critical: XCircle, 
  offline: Power,
  maintenance: Settings,
  
  // Electrical Status Icons
  energized: Zap,
  deenergized: Power,
  fault: XCircle,
  overload: AlertTriangle,
  undervoltage: AlertCircle,
  
  // Process Status Icons
  active: Activity,
  inactive: Pause,
  pending: Clock,
  processing: Settings,
  completed: CheckCircle2,
  failed: XCircle,
} as const

// Dot indicator component for minimal status display
const StatusDot: React.FC<{
  variant: string
  size: keyof typeof iconSizeConfig
  className?: string
}> = ({ variant, size, className }) => {
  const dotSize = {
    xs: "h-2 w-2",
    sm: "h-2.5 w-2.5",
    md: "h-3 w-3", 
    lg: "h-3.5 w-3.5",
    xl: "h-4 w-4",
  }[size]
  
  const dotColor = {
    operational: "bg-green-500",
    warning: "bg-amber-500",
    critical: "bg-red-500", 
    offline: "bg-gray-500",
    maintenance: "bg-blue-500",
    energized: "bg-emerald-500",
    deenergized: "bg-slate-500",
    fault: "bg-red-500",
    overload: "bg-orange-500",
    undervoltage: "bg-yellow-500",
    active: "bg-green-500",
    inactive: "bg-gray-400",
    pending: "bg-blue-500",
    processing: "bg-purple-500",
    completed: "bg-green-600",
    failed: "bg-red-500",
  }[variant] || "bg-gray-500"
  
  return (
    <div 
      className={cn(dotSize, dotColor, "rounded-full flex-shrink-0", className)}
      aria-hidden="true"
    />
  )
}

export interface StatusIndicatorProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof statusIndicatorVariants> {
  /** Status label text */
  label?: string
  /** Show status icon */
  showIcon?: boolean
  /** Show as dot indicator only */
  showDot?: boolean
  /** Custom icon to override default */
  icon?: LucideIcon
  /** Animate the icon (for processing states) */
  animate?: boolean
  /** Test identifier for automated testing */
  "data-testid"?: string
}

export const StatusIndicator = React.forwardRef<HTMLDivElement, StatusIndicatorProps>(
  (
    {
      variant = "operational",
      size = "md", 
      appearance = "default",
      label,
      showIcon = true,
      showDot = false,
      icon: CustomIcon,
      animate = false,
      className,
      "data-testid": testId,
      ...props
    },
    ref
  ) => {
    const IconComponent = CustomIcon || statusIconMap[variant || "operational"]
    const iconSize = iconSizeConfig[size || "md"]
    
    // For dot style, render minimal dot indicator
    if (appearance === "dot" || showDot) {
      return (
        <div
          ref={ref}
          className={cn("inline-flex items-center gap-1.5", className)}
          data-testid={testId || `status-indicator-${variant}-dot`}
          role="status"
          aria-label={label || `Status: ${variant}`}
          {...props}
        >
          <StatusDot variant={variant || "operational"} size={size || "md"} />
          {label && (
            <span className={cn("font-medium", iconSizeConfig[size || "md"].replace("h-", "text-").replace(" w-", ""))}>
              {label}
            </span>
          )}
        </div>
      )
    }
    
    return (
      <div
        ref={ref}
        className={cn(statusIndicatorVariants({ variant, size, appearance }), className)}
        data-testid={testId || `status-indicator-${variant}`}
        role="status"
        aria-label={label || `Status: ${variant}`}
        {...props}
      >
        {showIcon && IconComponent && (
          <IconComponent 
            className={cn(
              iconSize,
              animate && "animate-spin",
              // Processing and pending states get animation
              (variant === "processing" || variant === "pending") && "animate-pulse"
            )} 
            aria-hidden="true"
          />
        )}
        {label && (
          <span className="truncate">
            {label}
          </span>
        )}
      </div>
    )
  }
)

StatusIndicator.displayName = "StatusIndicator"

// Export types for external use
export type StatusIndicatorVariant = NonNullable<StatusIndicatorProps["variant"]>
export type StatusIndicatorSize = NonNullable<StatusIndicatorProps["size"]>
export type StatusIndicatorStyle = NonNullable<StatusIndicatorProps["style"]>

// Export variants and configurations for external use
export { statusIndicatorVariants, statusIconMap, iconSizeConfig }

// Utility functions for electrical engineering context
export const getElectricalStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    energized: "text-emerald-600",
    deenergized: "text-slate-600", 
    fault: "text-red-600",
    overload: "text-orange-600",
    undervoltage: "text-yellow-600",
    operational: "text-green-600",
    warning: "text-amber-600",
    critical: "text-red-600",
  }
  return colorMap[status] || "text-gray-600"
}

export const isElectricalStatus = (status: string): boolean => {
  const electricalStatuses = ["energized", "deenergized", "fault", "overload", "undervoltage"]
  return electricalStatuses.includes(status)
}

export const isSystemStatus = (status: string): boolean => {
  const systemStatuses = ["operational", "warning", "critical", "offline", "maintenance"]
  return systemStatuses.includes(status)
}

export const isProcessStatus = (status: string): boolean => {
  const processStatuses = ["active", "inactive", "pending", "processing", "completed", "failed"]
  return processStatuses.includes(status)
}