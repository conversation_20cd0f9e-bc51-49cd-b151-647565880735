/**
 * Atomic Components - Foundational Building Blocks
 * 
 * Universal atomic design components providing core functionality
 * for the Ultimate Electrical Designer application.
 * 
 * Architecture:
 * - Single responsibility components
 * - WCAG 2.1 AA accessibility compliance
 * - TypeScript strict mode compliance
 * - Consistent design system integration
 * - Performance optimized
 * - Full backward compatibility
 */

// Core Atoms
export { Button, type ButtonProps, type ButtonVariant, type ButtonSize, buttonVariants } from "./Button"
export { Input, type InputProps, type InputSize, type InputState, type InputVariant, inputVariants } from "./Input"
export { Label, type LabelProps, type LabelSize, type LabelWeight, type LabelState, labelVariants } from "./Label"
export { Icon, type IconProps, type IconName, type IconSize, type IconColor, iconVariants, iconMap, hasIcon, getAvailableIcons } from "./Icon"
export { Badge, type BadgeProps, type BadgeVariant, type BadgeSize, badgeVariants } from "./Badge"

// Enhanced Atoms - Phase 1 Implementation
export { 
  StatusIndicator, 
  type StatusIndicatorProps, 
  type StatusIndicatorVariant, 
  type StatusIndicatorSize, 
  type StatusIndicatorStyle,
  statusIndicatorVariants,
  statusIconMap,
  iconSizeConfig as statusIconSizeConfig,
  getElectricalStatusColor,
  isElectricalStatus,
  isSystemStatus,
  isProcessStatus
} from "./StatusIndicator"

export { 
  Avatar, 
  AvatarGroup,
  type AvatarProps, 
  type AvatarGroupProps,
  type AvatarVariant, 
  type AvatarSize,
  type UserRole,
  type UserStatus,
  avatarVariants,
  roleIndicatorVariants,
  roleIconMap,
  statusColors,
  getElectricalRoleIcon,
  getElectricalRoleColor,
  isValidElectricalRole
} from "./Avatar"

export { 
  ProgressBar, 
  CircularProgress,
  type ProgressBarProps, 
  type CircularProgressProps,
  type ProgressBarVariant, 
  type ProgressBarSize,
  progressBarVariants,
  progressFillVariants,
  shimmerVariants,
  getElectricalProgressVariant,
  getElectricalProgressColor,
  progressAnimationKeyframes
} from "./ProgressBar"

export { 
  Chip, 
  ChipGroup,
  type ChipProps, 
  type ChipGroupProps,
  type ChipVariant, 
  type ChipSize,
  chipVariants,
  closeButtonVariants,
  variantIconMap as chipVariantIconMap,
  getElectricalChipVariant,
  getElectricalChipIcon,
  createElectricalStatusChips
} from "./Chip"