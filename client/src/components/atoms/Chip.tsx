/**
 * Chip Atom - Compact Information Display Component
 * 
 * Atomic design chip component providing compact information display
 * for tags, labels, and status indicators with engineering-grade quality.
 * 
 * Features:
 * - Atomic design principles (single responsibility)
 * - WCAG 2.1 AA accessibility compliance
 * - TypeScript strict mode compliance
 * - Performance optimized with minimal footprint
 * - Consistent design system integration
 * - Professional electrical design standards
 * - Interactive capabilities with removable option
 */

import React from "react"
import type { VariantProps } from "class-variance-authority"
import { cva } from "class-variance-authority"
import { X, Check, AlertTriangle, Info, Zap, Wrench, Eye, Shield, User } from "lucide-react"

import { cn } from "@/lib/utils"

// Chip variants using CVA for consistent styling
const chipVariants = cva(
  "inline-flex items-center gap-1.5 font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-ring/50 focus:ring-offset-2",
  {
    variants: {
      variant: {
        default: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        primary: "bg-primary text-primary-foreground hover:bg-primary/90",
        secondary: "bg-muted text-muted-foreground hover:bg-muted/80",
        success: "bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-900/50",
        warning: "bg-amber-100 text-amber-800 hover:bg-amber-200 dark:bg-amber-900/30 dark:text-amber-400 dark:hover:bg-amber-900/50",
        danger: "bg-red-100 text-red-800 hover:bg-red-200 dark:bg-red-900/30 dark:text-red-400 dark:hover:bg-red-900/50",
        info: "bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-400 dark:hover:bg-blue-900/50",
        outline: "border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",
        // Electrical engineering specific variants
        electrical: "bg-yellow-100 text-yellow-800 hover:bg-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-400 dark:hover:bg-yellow-900/50",
        mechanical: "bg-orange-100 text-orange-800 hover:bg-orange-200 dark:bg-orange-900/30 dark:text-orange-400 dark:hover:bg-orange-900/50",
        safety: "bg-red-100 text-red-800 hover:bg-red-200 dark:bg-red-900/30 dark:text-red-400 dark:hover:bg-red-900/50",
        inspection: "bg-purple-100 text-purple-800 hover:bg-purple-200 dark:bg-purple-900/30 dark:text-purple-400 dark:hover:bg-purple-900/50",
        status: "bg-emerald-100 text-emerald-800 hover:bg-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-400 dark:hover:bg-emerald-900/50",
      },
      size: {
        xs: "px-2 py-0.5 text-xs rounded-full",
        sm: "px-2.5 py-1 text-xs rounded-full", 
        md: "px-3 py-1.5 text-sm rounded-full",
        lg: "px-4 py-2 text-sm rounded-lg",
        xl: "px-5 py-2.5 text-base rounded-lg",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "md",
    },
  }
)

// Icon size configuration
const iconSizeConfig = {
  xs: "h-3 w-3",
  sm: "h-3.5 w-3.5",
  md: "h-4 w-4",
  lg: "h-4 w-4", 
  xl: "h-5 w-5",
} as const

// Close button variants
const closeButtonVariants = cva(
  "rounded-full hover:bg-black/10 dark:hover:bg-white/10 transition-colors duration-150 flex items-center justify-center",
  {
    variants: {
      size: {
        xs: "h-3 w-3 ml-1",
        sm: "h-4 w-4 ml-1",
        md: "h-4 w-4 ml-1.5",
        lg: "h-5 w-5 ml-2",
        xl: "h-6 w-6 ml-2",
      },
    },
    defaultVariants: {
      size: "md",
    },
  }
)

// Variant to icon mapping for electrical context
const variantIconMap = {
  success: Check,
  warning: AlertTriangle,
  danger: AlertTriangle,
  info: Info,
  electrical: Zap,
  mechanical: Wrench,
  safety: Shield,
  inspection: Eye,
  status: Check,
} as const

export interface ChipProps
  extends React.HTMLAttributes<HTMLElement>,
    VariantProps<typeof chipVariants> {
  /** Chip label text */
  label?: string
  /** Leading icon */
  icon?: React.ComponentType<{ className?: string }>
  /** Show default variant icon */
  showIcon?: boolean
  /** Make chip removable with close button */
  removable?: boolean
  /** Remove callback function */
  onRemove?: () => void
  /** Click callback function */
  onClick?: () => void
  /** Disabled state */
  disabled?: boolean
  /** Selected state for toggle chips */
  selected?: boolean
  /** Avatar/image source for user chips */
  avatar?: string
  /** Custom close icon */
  closeIcon?: React.ComponentType<{ className?: string }>
  /** Test identifier for automated testing */
  "data-testid"?: string
  /** Children content (alternative to label) */
  children?: React.ReactNode
}

export const Chip = React.forwardRef<HTMLElement, ChipProps>(
  (
    {
      variant = "default",
      size = "md",
      label,
      icon: Icon,
      showIcon = false,
      removable = false,
      onRemove,
      onClick,
      disabled = false,
      selected = false,
      avatar,
      closeIcon: CloseIcon = X,
      className,
      "data-testid": testId,
      children,
      ...props
    },
    ref
  ) => {
    const iconSize = iconSizeConfig[size || "md"]
    const DefaultIcon = variant && variant in variantIconMap 
      ? variantIconMap[variant as keyof typeof variantIconMap]
      : null
      
    const DisplayIcon = Icon || (showIcon ? DefaultIcon : null)
    
    const handleClick = React.useCallback((event: React.MouseEvent) => {
      if (disabled) return
      
      event.preventDefault()
      onClick?.()
    }, [disabled, onClick])
    
    const handleRemove = React.useCallback((event: React.MouseEvent) => {
      event.stopPropagation()
      if (disabled) return
      
      onRemove?.()
    }, [disabled, onRemove])
    
    const isClickable = !disabled && (onClick || selected !== undefined)
    const Component = isClickable ? "button" : "div"
    
    return (
      <Component
        ref={ref as any}
        className={cn(
          chipVariants({ variant, size }),
          disabled && "opacity-50 cursor-not-allowed pointer-events-none",
          selected && "ring-2 ring-ring/50",
          isClickable && "cursor-pointer",
          className
        )}
        onClick={isClickable ? handleClick : undefined}
        disabled={isClickable ? disabled : undefined}
        data-testid={testId || "chip"}
        role={isClickable ? "button" : undefined}
        tabIndex={isClickable ? 0 : undefined}
        aria-pressed={selected !== undefined ? selected : undefined}
        {...props}
      >
        {/* Avatar */}
        {avatar && (
          <img
            src={avatar}
            alt=""
            className={cn(
              "rounded-full object-cover",
              size === "xs" ? "h-3 w-3" :
              size === "sm" ? "h-4 w-4" :
              size === "md" ? "h-5 w-5" :
              size === "lg" ? "h-6 w-6" : "h-7 w-7"
            )}
          />
        )}
        
        {/* Icon */}
        {DisplayIcon && !avatar && (
          <DisplayIcon className={cn(iconSize, "shrink-0")} aria-hidden="true" />
        )}
        
        {/* Content */}
        <span className="truncate">
          {children || label}
        </span>
        
        {/* Remove button */}
        {removable && onRemove && (
          <button
            type="button"
            onClick={handleRemove}
            className={cn(closeButtonVariants({ size }))}
            aria-label="Remove"
            tabIndex={-1}
          >
            <CloseIcon 
              className={cn(
                size === "xs" ? "h-2 w-2" :
                size === "sm" ? "h-2.5 w-2.5" :
                size === "md" ? "h-3 w-3" :
                size === "lg" ? "h-3.5 w-3.5" : "h-4 w-4"
              )} 
            />
          </button>
        )}
      </Component>
    )
  }
)

Chip.displayName = "Chip"

// Chip Group Component for managing multiple related chips
export interface ChipGroupProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Array of chip props */
  chips: (ChipProps & { id: string })[]
  /** Allow multiple selection */
  multiSelect?: boolean
  /** Selected chip IDs */
  selectedIds?: string[]
  /** Selection change callback */
  onSelectionChange?: (selectedIds: string[]) => void
  /** Chip removal callback */
  onRemove?: (id: string) => void
  /** Size of chips in group */
  size?: ChipProps["size"]
  /** Variant of chips in group */
  variant?: ChipProps["variant"]
  /** Spacing between chips */
  spacing?: "tight" | "normal" | "loose"
  /** Test identifier for automated testing */
  "data-testid"?: string
}

export const ChipGroup = React.forwardRef<HTMLDivElement, ChipGroupProps>(
  (
    {
      chips,
      multiSelect = false,
      selectedIds = [],
      onSelectionChange,
      onRemove,
      size = "md",
      variant = "default",
      spacing = "normal",
      className,
      "data-testid": testId,
      ...props
    },
    ref
  ) => {
    const spacingClasses = {
      tight: "gap-1",
      normal: "gap-2",
      loose: "gap-3",
    }[spacing]
    
    const handleSelection = React.useCallback((chipId: string) => {
      if (!onSelectionChange) return
      
      let newSelectedIds: string[]
      
      if (multiSelect) {
        newSelectedIds = selectedIds.includes(chipId)
          ? selectedIds.filter(id => id !== chipId)
          : [...selectedIds, chipId]
      } else {
        newSelectedIds = selectedIds.includes(chipId) ? [] : [chipId]
      }
      
      onSelectionChange(newSelectedIds)
    }, [multiSelect, selectedIds, onSelectionChange])
    
    const handleRemove = React.useCallback((chipId: string) => {
      onRemove?.(chipId)
      
      // Remove from selection if selected
      if (selectedIds.includes(chipId) && onSelectionChange) {
        onSelectionChange(selectedIds.filter(id => id !== chipId))
      }
    }, [onRemove, selectedIds, onSelectionChange])
    
    return (
      <div
        ref={ref}
        className={cn("flex flex-wrap items-center", spacingClasses, className)}
        data-testid={testId || "chip-group"}
        role="group"
        {...props}
      >
        {chips.map(({ id, ...chipProps }) => (
          <Chip
            key={id}
            size={size}
            variant={variant}
            selected={selectedIds.includes(id)}
            onClick={onSelectionChange ? () => handleSelection(id) : undefined}
            onRemove={onRemove ? () => handleRemove(id) : chipProps.onRemove}
            data-testid={`chip-group-item-${id}`}
            {...chipProps}
          />
        ))}
      </div>
    )
  }
)

ChipGroup.displayName = "ChipGroup"

// Export types for external use
export type ChipVariant = NonNullable<ChipProps["variant"]>
export type ChipSize = NonNullable<ChipProps["size"]>

// Export variants for external use
export { chipVariants, closeButtonVariants, variantIconMap }

// Utility functions for electrical engineering context
export const getElectricalChipVariant = (category: string): ChipVariant => {
  const categoryMap: Record<string, ChipVariant> = {
    electrical: "electrical",
    mechanical: "mechanical", 
    safety: "safety",
    inspection: "inspection",
    power: "electrical",
    control: "info",
    protection: "safety",
    instrumentation: "inspection",
    hvac: "mechanical",
  }
  return categoryMap[category] || "default"
}

export const getElectricalChipIcon = (category: string) => {
  const iconMap: Record<string, React.ComponentType<{ className?: string }>> = {
    electrical: Zap,
    mechanical: Wrench,
    safety: Shield,
    inspection: Eye,
    user: User,
  }
  return iconMap[category]
}

export const createElectricalStatusChips = (statuses: Array<{
  id: string
  status: string
  label: string
  category?: string
}>): (ChipProps & { id: string })[] => {
  return statuses.map(({ id, label, category = "status" }) => ({
    id,
    label,
    variant: getElectricalChipVariant(category),
    icon: getElectricalChipIcon(category),
    showIcon: true,
    size: "sm" as const,
  }))
}