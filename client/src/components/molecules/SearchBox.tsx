/**
 * SearchBox Molecule - Search Input Component
 * 
 * Atomic design search box molecule combining atoms to provide
 * comprehensive search functionality for electrical engineering applications.
 * 
 * Features:
 * - Atomic design principles (combining atoms)
 * - WCAG 2.1 AA accessibility compliance
 * - TypeScript strict mode compliance
 * - Performance optimized with debouncing
 * - Consistent design system integration
 * - Professional electrical design standards
 * - Advanced search features
 */

import React from "react"
import type { VariantProps } from "class-variance-authority"
import { cva } from "class-variance-authority"
import { Search, X, SlidersHorizontal, Loader2 } from "lucide-react"

import { cn } from "@/lib/utils"
import { Input } from "@/components/atoms/Input"
import { Button } from "@/components/atoms/Button"
import { Badge } from "@/components/atoms/Badge"

// Search box variants using CVA for consistent styling
const searchBoxVariants = cva(
  "relative w-full transition-all duration-200",
  {
    variants: {
      variant: {
        default: "",
        outlined: "border rounded-lg p-1",
        filled: "bg-muted rounded-lg p-1",
      },
      size: {
        sm: "",
        md: "",
        lg: "",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "md",
    },
  }
)

export interface SearchFilter {
  key: string
  label: string
  value: string | number | boolean
  type?: "text" | "number" | "boolean" | "select"
  options?: { label: string; value: string | number }[]
}

export interface SearchBoxProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, "size" | "onChange" | "onSubmit">,
    VariantProps<typeof searchBoxVariants> {
  /** Search value */
  value?: string
  /** Search change callback */
  onChange?: (value: string) => void
  /** Search submit callback */
  onSubmit?: (value: string) => void
  /** Clear search callback */
  onClear?: () => void
  /** Filter toggle callback */
  onFilterToggle?: () => void
  /** Active filters */
  filters?: SearchFilter[]
  /** Remove filter callback */
  onRemoveFilter?: (filterKey: string) => void
  /** Show filter button */
  showFilterButton?: boolean
  /** Show clear button */
  showClearButton?: boolean
  /** Loading state */
  loading?: boolean
  /** Debounce delay in ms */
  debounceDelay?: number
  /** Search results count */
  resultsCount?: number
  /** Show results count */
  showResultsCount?: boolean
  /** Recent searches */
  recentSearches?: string[]
  /** Show recent searches */
  showRecentSearches?: boolean
  /** Recent search select callback */
  onRecentSearchSelect?: (search: string) => void
  /** Custom search icon */
  searchIcon?: React.ComponentType<{ className?: string }>
  /** Test identifier for automated testing */
  "data-testid"?: string
}

export const SearchBox = React.forwardRef<HTMLInputElement, SearchBoxProps>(
  (
    {
      variant = "default",
      size = "md",
      value = "",
      onChange,
      onSubmit,
      onClear,
      onFilterToggle,
      filters = [],
      onRemoveFilter,
      showFilterButton = false,
      showClearButton = true,
      loading = false,
      debounceDelay = 300,
      resultsCount,
      showResultsCount = false,
      recentSearches = [],
      showRecentSearches = false,
      onRecentSearchSelect,
      searchIcon: CustomSearchIcon,
      placeholder = "Search...",
      className,
      "data-testid": testId,
      ...props
    },
    ref
  ) => {
    const [internalValue, setInternalValue] = React.useState(value || "")
    const [showRecent, setShowRecent] = React.useState(false)
    const [focused, setFocused] = React.useState(false)
    const debounceRef = React.useRef<NodeJS.Timeout | null>(null)
    const inputRef = React.useRef<HTMLInputElement>(null)
    
    // Use forwarded ref or internal ref
    const searchInputRef = ref || inputRef
    
    const SearchIcon = CustomSearchIcon || Search
    
    // Debounced onChange
    const debouncedOnChange = React.useCallback(
      (searchValue: string) => {
        if (debounceRef.current) {
          clearTimeout(debounceRef.current)
        }
        
        debounceRef.current = setTimeout(() => {
          onChange?.(searchValue)
        }, debounceDelay)
      },
      [onChange, debounceDelay]
    )
    
    // Handle input change
    const handleInputChange = React.useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        const newValue = e.target.value
        setInternalValue(newValue)
        debouncedOnChange(newValue)
      },
      [debouncedOnChange]
    )
    
    // Handle form submit
    const handleSubmit = React.useCallback(
      (e: React.FormEvent) => {
        e.preventDefault()
        onSubmit?.(internalValue)
        setShowRecent(false)
      },
      [onSubmit, internalValue]
    )
    
    // Handle clear
    const handleClear = React.useCallback(() => {
      setInternalValue("")
      onChange?.("")
      onClear?.()
      if (searchInputRef && "current" in searchInputRef) {
        searchInputRef.current?.focus()
      }
    }, [onChange, onClear, searchInputRef])
    
    // Handle focus
    const handleFocus = React.useCallback(() => {
      setFocused(true)
      if (showRecentSearches && recentSearches.length > 0 && !internalValue) {
        setShowRecent(true)
      }
    }, [showRecentSearches, recentSearches, internalValue])
    
    // Handle blur
    const handleBlur = React.useCallback(() => {
      setFocused(false)
      // Delay hiding recent searches to allow for clicks
      setTimeout(() => setShowRecent(false), 200)
    }, [])
    
    // Handle recent search selection
    const handleRecentSearchSelect = React.useCallback(
      (search: string) => {
        setInternalValue(search)
        onChange?.(search)
        onRecentSearchSelect?.(search)
        setShowRecent(false)
      },
      [onChange, onRecentSearchSelect]
    )
    
    // Handle key navigation
    const handleKeyDown = React.useCallback(
      (e: React.KeyboardEvent) => {
        if (e.key === "Escape") {
          setShowRecent(false)
          if (searchInputRef && "current" in searchInputRef) {
            searchInputRef.current?.blur()
          }
        }
      },
      [searchInputRef]
    )
    
    // Update internal value when prop changes
    React.useEffect(() => {
      setInternalValue(value)
    }, [value])
    
    // Cleanup debounce on unmount
    React.useEffect(() => {
      return () => {
        if (debounceRef.current) {
          clearTimeout(debounceRef.current)
        }
      }
    }, [])
    
    return (
      <div className={cn(searchBoxVariants({ variant, size }), className)}>
        <form onSubmit={handleSubmit} role="search">
          <div className="relative">
            {/* Main search input */}
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                {loading ? (
                  <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                ) : (
                  <SearchIcon className="h-4 w-4 text-muted-foreground" />
                )}
              </div>
              
              <Input
                ref={searchInputRef}
                type="text"
                value={internalValue}
                onChange={handleInputChange}
                onFocus={handleFocus}
                onBlur={handleBlur}
                onKeyDown={handleKeyDown}
                placeholder={placeholder}
                className={cn(
                  "pl-10",
                  (showClearButton || showFilterButton) && "pr-20",
                  focused && "ring-2 ring-ring/20"
                )}
                data-testid={testId || "search-input"}
                {...props}
              />
              
              {/* Action buttons */}
              <div className="absolute inset-y-0 right-0 flex items-center gap-1 pr-2">
                {/* Clear button */}
                {showClearButton && internalValue && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={handleClear}
                    className="h-6 w-6 p-0 hover:bg-muted/80"
                    aria-label="Clear search"
                    data-testid="search-clear-button"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                )}
                
                {/* Filter button */}
                {showFilterButton && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={onFilterToggle}
                    className={cn(
                      "h-6 w-6 p-0 hover:bg-muted/80",
                      filters.length > 0 && "text-primary bg-primary/10"
                    )}
                    aria-label="Toggle filters"
                    data-testid="search-filter-button"
                  >
                    <SlidersHorizontal className="h-3 w-3" />
                  </Button>
                )}
              </div>
            </div>
            
            {/* Recent searches dropdown */}
            {showRecent && recentSearches.length > 0 && (
              <div className="absolute top-full left-0 right-0 z-50 mt-1 bg-background border border-border rounded-md shadow-lg max-h-60 overflow-auto">
                <div className="p-2">
                  <div className="text-xs font-medium text-muted-foreground mb-2">
                    Recent Searches
                  </div>
                  <div className="space-y-1">
                    {recentSearches.map((search, index) => (
                      <button
                        key={index}
                        type="button"
                        onClick={() => handleRecentSearchSelect(search)}
                        className="w-full text-left px-2 py-1 text-sm hover:bg-muted rounded-sm transition-colors"
                        data-testid={`recent-search-${index}`}
                      >
                        {search}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        </form>
        
        {/* Active filters */}
        {filters.length > 0 && (
          <div className="flex flex-wrap items-center gap-1 mt-2">
            <span className="text-xs text-muted-foreground">Filters:</span>
            {filters.map((filter) => (
              <Badge
                key={filter.key}
                variant="secondary"
                size="sm"
                className="text-xs"
              >
                <span className="mr-1">{filter.label}:</span>
                <span className="font-medium">{String(filter.value)}</span>
                {onRemoveFilter && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => onRemoveFilter(filter.key)}
                    className="h-3 w-3 p-0 ml-1 hover:bg-muted/80"
                    aria-label={`Remove ${filter.label} filter`}
                  >
                    <X className="h-2 w-2" />
                  </Button>
                )}
              </Badge>
            ))}
          </div>
        )}
        
        {/* Results count */}
        {showResultsCount && resultsCount !== undefined && (
          <div className="text-xs text-muted-foreground mt-1">
            {resultsCount === 0 ? "No results found" : 
             resultsCount === 1 ? "1 result" :
             `${resultsCount.toLocaleString()} results`}
          </div>
        )}
      </div>
    )
  }
)

SearchBox.displayName = "SearchBox"

// Convenience components for electrical engineering contexts
export const ComponentSearchBox = React.forwardRef<HTMLInputElement, 
  Omit<SearchBoxProps, "placeholder">
>(({ ...props }, ref) => (
  <SearchBox
    ref={ref}
    placeholder="Search components, categories, specifications..."
    showFilterButton
    showResultsCount
    {...props}
  />
))
ComponentSearchBox.displayName = "ComponentSearchBox"

export const ProjectSearchBox = React.forwardRef<HTMLInputElement, 
  Omit<SearchBoxProps, "placeholder">
>(({ ...props }, ref) => (
  <SearchBox
    ref={ref}
    placeholder="Search projects, tasks, team members..."
    showFilterButton
    showRecentSearches
    {...props}
  />
))
ProjectSearchBox.displayName = "ProjectSearchBox"

export const EquipmentSearchBox = React.forwardRef<HTMLInputElement, 
  Omit<SearchBoxProps, "placeholder">
>(({ ...props }, ref) => (
  <SearchBox
    ref={ref}
    placeholder="Search equipment, systems, locations..."
    showFilterButton
    showResultsCount
    {...props}
  />
))
EquipmentSearchBox.displayName = "EquipmentSearchBox"

// Export types for external use
export type SearchBoxVariant = NonNullable<SearchBoxProps["variant"]>
export type SearchBoxSize = NonNullable<SearchBoxProps["size"]>

// Export variants for external use
export { searchBoxVariants }

// Utility functions for electrical search contexts
export const createElectricalSearchFilters = (): SearchFilter[] => [
  {
    key: "category",
    label: "Category",
    value: "",
    type: "select",
    options: [
      { label: "All Categories", value: "" },
      { label: "Motors", value: "motors" },
      { label: "Transformers", value: "transformers" },
      { label: "Switchgear", value: "switchgear" },
      { label: "Cables", value: "cables" },
      { label: "Protection", value: "protection" },
      { label: "Control", value: "control" },
      { label: "Instrumentation", value: "instrumentation" },
    ],
  },
  {
    key: "voltage",
    label: "Voltage Range",
    value: "",
    type: "select",
    options: [
      { label: "All Voltages", value: "" },
      { label: "Low Voltage (<1kV)", value: "low" },
      { label: "Medium Voltage (1-35kV)", value: "medium" },
      { label: "High Voltage (>35kV)", value: "high" },
    ],
  },
  {
    key: "status",
    label: "Status",
    value: "",
    type: "select",
    options: [
      { label: "All Statuses", value: "" },
      { label: "Active", value: "active" },
      { label: "Available", value: "available" },
      { label: "Maintenance", value: "maintenance" },
      { label: "Out of Service", value: "out_of_service" },
    ],
  },
]

export const formatSearchQuery = (value: string, filters: SearchFilter[]): string => {
  const activeFilters = filters.filter(f => f.value !== "" && f.value !== false)
  
  if (activeFilters.length === 0) return value
  
  const filterString = activeFilters
    .map(f => `${f.key}:${f.value}`)
    .join(" ")
    
  return value ? `${value} ${filterString}` : filterString
}