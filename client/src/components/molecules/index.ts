/**
 * Molecular Components - Composed Building Blocks
 * 
 * Atomic design molecules combining multiple atoms to create
 * more complex, reusable interface components.
 * 
 * Architecture:
 * - Composed of atomic components
 * - WCAG 2.1 AA accessibility compliance
 * - TypeScript strict mode compliance
 * - Consistent design system integration
 * - Performance optimized
 * - Engineering-grade quality
 */

// Core Molecules
export { 
  InputField, 
  type InputFieldProps 
} from "./InputField"

export { 
  ButtonGroup, 
  ActionButtonGroup,
  type ButtonGroupProps, 
  type ActionButtonGroupProps,
  type ButtonConfig,
  type ButtonGroupOrientation,
  type ButtonGroupSpacing,
  buttonGroupVariants
} from "./ButtonGroup"

// Enhanced Molecules - Phase 2 Implementation
export { 
  AlertCard,
  InfoAlertCard,
  SuccessAlertCard,
  WarningAlertCard,
  DangerAlertCard,
  ElectricalAlertCard,
  FaultAlertCard,
  type AlertCardProps,
  type AlertCardVariant,
  type AlertCardSize,
  alertCardVariants,
  variantIconMap as alertVariantIconMap,
  getElectricalAlertVariant,
  createElectricalAlert
} from "./AlertCard"

export { 
  HealthIndicator,
  PowerSystemHealth,
  MotorHealth,
  TransformerHealth,
  type HealthIndicatorProps,
  type HealthStatus,
  type HealthMetric,
  type HealthIndicatorVariant,
  type HealthIndicatorSize,
  healthIndicatorVariants,
  healthStatusConfig,
  createElectricalHealthMetrics,
  determineOverallHealth
} from "./HealthIndicator"

export { 
  SearchBox,
  ComponentSearchBox,
  ProjectSearchBox,
  EquipmentSearchBox,
  type SearchBoxProps,
  type SearchFilter,
  type SearchBoxVariant,
  type SearchBoxSize,
  searchBoxVariants,
  createElectricalSearchFilters,
  formatSearchQuery
} from "./SearchBox"

export { 
  StatusCard,
  PowerSystemStatus,
  MotorStatus,
  TransformerStatus,
  PanelStatus,
  type StatusCardProps,
  type StatusCardMetric,
  type StatusCardVariant,
  type StatusCardSize,
  type StatusCardStatus,
  statusCardVariants,
  createElectricalStatusMetrics,
  determineSystemStatus
} from "./StatusCard"