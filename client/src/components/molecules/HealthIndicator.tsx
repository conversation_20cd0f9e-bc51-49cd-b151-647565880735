/**
 * HealthIndicator Molecule - System Health Display Component
 * 
 * Atomic design health indicator molecule combining atoms to display
 * system health status for electrical engineering applications.
 * 
 * Features:
 * - Atomic design principles (combining atoms)
 * - WCAG 2.1 AA accessibility compliance
 * - TypeScript strict mode compliance
 * - Performance optimized with real-time updates
 * - Consistent design system integration
 * - Professional electrical design standards
 * - Multiple health metrics visualization
 */

import React from "react"
import type { VariantProps } from "class-variance-authority"
import { cva } from "class-variance-authority"
import { Activity, AlertTriangle, CheckCircle2, XCircle, TrendingUp, TrendingDown } from "lucide-react"

import { cn } from "@/lib/utils"
import { StatusIndicator } from "@/components/atoms/StatusIndicator"
import { ProgressBar, CircularProgress } from "@/components/atoms/ProgressBar"
import { Badge } from "@/components/atoms/Badge"

// Health indicator variants using CVA for consistent styling
const healthIndicatorVariants = cva(
  "rounded-lg border transition-all duration-300",
  {
    variants: {
      variant: {
        card: "p-4 bg-background",
        compact: "p-2 bg-background/50",
        inline: "p-1 bg-transparent border-none",
      },
      status: {
        healthy: "border-green-200 bg-green-50/50 dark:border-green-800 dark:bg-green-950/20",
        warning: "border-amber-200 bg-amber-50/50 dark:border-amber-800 dark:bg-amber-950/20",
        critical: "border-red-200 bg-red-50/50 dark:border-red-800 dark:bg-red-950/20",
        unknown: "border-gray-200 bg-gray-50/50 dark:border-gray-700 dark:bg-gray-950/20",
      },
      size: {
        sm: "text-sm",
        md: "text-sm",
        lg: "text-base",
      },
    },
    defaultVariants: {
      variant: "card",
      status: "unknown",
      size: "md",
    },
  }
)

// Health status configuration
const healthStatusConfig = {
  healthy: {
    label: "Healthy",
    icon: CheckCircle2,
    color: "text-green-600",
    bgColor: "bg-green-100",
    statusVariant: "operational" as const,
  },
  warning: {
    label: "Warning", 
    icon: AlertTriangle,
    color: "text-amber-600",
    bgColor: "bg-amber-100",
    statusVariant: "warning" as const,
  },
  critical: {
    label: "Critical",
    icon: XCircle,
    color: "text-red-600", 
    bgColor: "bg-red-100",
    statusVariant: "critical" as const,
  },
  unknown: {
    label: "Unknown",
    icon: Activity,
    color: "text-gray-600",
    bgColor: "bg-gray-100",
    statusVariant: "offline" as const,
  },
} as const

export type HealthStatus = keyof typeof healthStatusConfig

export interface HealthMetric {
  name: string
  value: number
  max: number
  unit?: string
  status: HealthStatus
  trend?: "up" | "down" | "stable"
  threshold?: {
    warning: number
    critical: number
  }
}

export interface HealthIndicatorProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof healthIndicatorVariants> {
  /** System or component name */
  name: string
  /** Overall health status */
  status: HealthStatus
  /** Health metrics to display */
  metrics?: HealthMetric[]
  /** Show detailed metrics */
  showMetrics?: boolean
  /** Show trend indicators */
  showTrends?: boolean
  /** Last updated timestamp */
  lastUpdated?: Date
  /** Show last updated time */
  showLastUpdated?: boolean
  /** Refresh callback */
  onRefresh?: () => void
  /** Loading state */
  loading?: boolean
  /** Custom status message */
  message?: string
  /** Progress display style */
  progressStyle?: "bar" | "circular"
  /** Test identifier for automated testing */
  "data-testid"?: string
}

export const HealthIndicator = React.forwardRef<HTMLDivElement, HealthIndicatorProps>(
  (
    {
      variant = "card",
      size = "md",
      name,
      status,
      metrics = [],
      showMetrics = true,
      showTrends = true,
      lastUpdated,
      showLastUpdated = false,
      onRefresh,
      loading = false,
      message,
      progressStyle = "bar",
      className,
      "data-testid": testId,
      ...props
    },
    ref
  ) => {
    const statusConfig = healthStatusConfig[status]
    
    const formatLastUpdated = React.useCallback((date: Date) => {
      const now = new Date()
      const diff = Math.floor((now.getTime() - date.getTime()) / 1000)
      
      if (diff < 60) return "Just now"
      if (diff < 3600) return `${Math.floor(diff / 60)}m ago`
      if (diff < 86400) return `${Math.floor(diff / 3600)}h ago`
      return `${Math.floor(diff / 86400)}d ago`
    }, [])
    
    const getMetricStatus = React.useCallback((metric: HealthMetric): HealthStatus => {
      if (!metric.threshold) return metric.status
      
      const percentage = (metric.value / metric.max) * 100
      
      if (percentage >= metric.threshold.critical) return "critical"
      if (percentage >= metric.threshold.warning) return "warning"
      return "healthy"
    }, [])
    
    const getTrendIcon = (trend: "up" | "down" | "stable") => {
      switch (trend) {
        case "up":
          return <TrendingUp className="h-3 w-3 text-green-600" />
        case "down":
          return <TrendingDown className="h-3 w-3 text-red-600" />
        default:
          return null
      }
    }
    
    return (
      <div
        ref={ref}
        className={cn(
          healthIndicatorVariants({ variant, status, size }),
          loading && "animate-pulse",
          className
        )}
        data-testid={testId || `health-indicator-${name.toLowerCase().replace(/\s+/g, "-")}`}
        role="status"
        aria-label={`Health status for ${name}: ${statusConfig.label}`}
        {...props}
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <StatusIndicator
              variant={statusConfig.statusVariant}
              size={size === "sm" ? "xs" : size === "lg" ? "md" : "sm"}
              showIcon
              animate={loading}
            />
            <div>
              <h3 className={cn(
                "font-medium text-foreground",
                size === "sm" ? "text-xs" : size === "lg" ? "text-base" : "text-sm"
              )}>
                {name}
              </h3>
              {message && (
                <p className="text-xs text-muted-foreground mt-0.5">
                  {message}
                </p>
              )}
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Badge variant="secondary" size="sm">
              {statusConfig.label}
            </Badge>
            
            {onRefresh && (
              <button
                onClick={onRefresh}
                disabled={loading}
                className="p-1 hover:bg-background/80 rounded-sm transition-colors"
                aria-label="Refresh health status"
              >
                <Activity className={cn("h-4 w-4", loading && "animate-spin")} />
              </button>
            )}
          </div>
        </div>
        
        {/* Metrics */}
        {showMetrics && metrics.length > 0 && (
          <div className="space-y-3">
            {metrics.map((metric, index) => {
              const metricStatus = getMetricStatus(metric)
              const metricConfig = healthStatusConfig[metricStatus]
              
              return (
                <div key={`${metric.name}-${index}`} className="space-y-1">
                  <div className="flex items-center justify-between text-xs">
                    <div className="flex items-center gap-1">
                      <span className="font-medium text-foreground">
                        {metric.name}
                      </span>
                      {showTrends && metric.trend && getTrendIcon(metric.trend)}
                    </div>
                    <div className="flex items-center gap-1">
                      <span className={cn("font-mono", metricConfig.color)}>
                        {metric.value}
                        {metric.unit && <span className="text-muted-foreground">
                          {metric.unit}
                        </span>}
                      </span>
                      {metric.max && (
                        <span className="text-muted-foreground">
                          / {metric.max}{metric.unit}
                        </span>
                      )}
                    </div>
                  </div>
                  
                  {/* Progress visualization */}
                  {progressStyle === "circular" && variant === "card" ? (
                    <div className="flex justify-center pt-2">
                      <CircularProgress
                        value={metric.value}
                        max={metric.max}
                        size={60}
                        strokeWidth={6}
                        variant={metricStatus === "healthy" ? "success" : metricStatus === "warning" ? "warning" : "danger"}
                        showValue
                        unit={metric.unit}
                        precision={0}
                      />
                    </div>
                  ) : (
                    <ProgressBar
                      value={metric.value}
                      max={metric.max}
                      size={size === "sm" ? "xs" : "sm"}
                      variant={metricStatus === "healthy" ? "success" : metricStatus === "warning" ? "warning" : "danger"}
                      animated={loading}
                      helperText={metric.threshold ? 
                        `Warning: ${metric.threshold.warning}${metric.unit}, Critical: ${metric.threshold.critical}${metric.unit}` : 
                        undefined
                      }
                    />
                  )}
                </div>
              )
            })}
          </div>
        )}
        
        {/* Last Updated */}
        {showLastUpdated && lastUpdated && (
          <div className="mt-3 pt-2 border-t border-border/50">
            <p className="text-xs text-muted-foreground">
              Last updated: {formatLastUpdated(lastUpdated)}
            </p>
          </div>
        )}
      </div>
    )
  }
)

HealthIndicator.displayName = "HealthIndicator"

// Convenience components for common electrical systems
export const PowerSystemHealth = React.forwardRef<HTMLDivElement, 
  Omit<HealthIndicatorProps, "name"> & { systemName?: string }
>(({ systemName = "Power System", ...props }, ref) => (
  <HealthIndicator ref={ref} name={systemName} {...props} />
))
PowerSystemHealth.displayName = "PowerSystemHealth"

export const MotorHealth = React.forwardRef<HTMLDivElement, 
  Omit<HealthIndicatorProps, "name"> & { motorName?: string }
>(({ motorName = "Motor", ...props }, ref) => (
  <HealthIndicator ref={ref} name={motorName} {...props} />
))
MotorHealth.displayName = "MotorHealth"

export const TransformerHealth = React.forwardRef<HTMLDivElement, 
  Omit<HealthIndicatorProps, "name"> & { transformerName?: string }
>(({ transformerName = "Transformer", ...props }, ref) => (
  <HealthIndicator ref={ref} name={transformerName} {...props} />
))
TransformerHealth.displayName = "TransformerHealth"

// Export types for external use
export type HealthIndicatorVariant = NonNullable<HealthIndicatorProps["variant"]>
export type HealthIndicatorSize = NonNullable<HealthIndicatorProps["size"]>

// Export variants and configurations for external use
export { healthIndicatorVariants, healthStatusConfig }

// Utility functions for electrical systems
export const createElectricalHealthMetrics = (
  systemType: "power" | "motor" | "transformer" | "panel"
): HealthMetric[] => {
  const baseMetrics = [
    {
      name: "Voltage",
      value: 480,
      max: 500, 
      unit: "V",
      status: "healthy" as HealthStatus,
      threshold: { warning: 460, critical: 440 },
    },
    {
      name: "Current",
      value: 45,
      max: 100,
      unit: "A", 
      status: "healthy" as HealthStatus,
      threshold: { warning: 80, critical: 95 },
    },
  ]
  
  const systemSpecific: Record<string, HealthMetric[]> = {
    power: [
      ...baseMetrics,
      {
        name: "Power Factor",
        value: 0.92,
        max: 1.0,
        unit: "",
        status: "healthy" as HealthStatus,
        threshold: { warning: 0.85, critical: 0.8 },
      },
      {
        name: "THD",
        value: 3.2,
        max: 10,
        unit: "%",
        status: "healthy" as HealthStatus,
        threshold: { warning: 5, critical: 8 },
      },
    ],
    motor: [
      ...baseMetrics,
      {
        name: "Temperature",
        value: 68,
        max: 85,
        unit: "°C",
        status: "healthy" as HealthStatus,
        threshold: { warning: 75, critical: 80 },
      },
      {
        name: "Vibration",
        value: 2.1,
        max: 5,
        unit: "mm/s",
        status: "healthy" as HealthStatus,
        threshold: { warning: 3.5, critical: 4.5 },
      },
    ],
    transformer: [
      ...baseMetrics,
      {
        name: "Oil Temperature",
        value: 55,
        max: 90,
        unit: "°C",
        status: "healthy" as HealthStatus,
        threshold: { warning: 70, critical: 85 },
      },
    ],
    panel: baseMetrics,
  }
  
  return systemSpecific[systemType] || baseMetrics
}

export const determineOverallHealth = (metrics: HealthMetric[]): HealthStatus => {
  if (metrics.some(m => m.status === "critical")) return "critical"
  if (metrics.some(m => m.status === "warning")) return "warning"
  if (metrics.every(m => m.status === "healthy")) return "healthy"
  return "unknown"
}