/**
 * StatusCard Molecule - Status Display Component
 * 
 * Atomic design status card molecule combining atoms to display
 * comprehensive status information for electrical engineering systems.
 * 
 * Features:
 * - Atomic design principles (combining atoms)
 * - WCAG 2.1 AA accessibility compliance
 * - TypeScript strict mode compliance
 * - Performance optimized with conditional rendering
 * - Consistent design system integration
 * - Professional electrical design standards
 * - Real-time status updates
 */

import React from "react"
import type { VariantProps } from "class-variance-authority"
import { cva } from "class-variance-authority"
import { Activity, Clock, TrendingUp, TrendingDown, MoreHorizontal, RefreshCw, type LucideIcon } from "lucide-react"

import { cn } from "@/lib/utils"
import { StatusIndicator } from "@/components/atoms/StatusIndicator"
import { Button } from "@/components/atoms/Button"

// Status card variants using CVA for consistent styling
const statusCardVariants = cva(
  "rounded-lg border bg-card transition-all duration-300 hover:shadow-sm",
  {
    variants: {
      variant: {
        default: "border-border",
        elevated: "border-border shadow-sm",
        flat: "border-none bg-muted/30",
      },
      size: {
        sm: "p-3",
        md: "p-4",
        lg: "p-6",
      },
      status: {
        normal: "border-l-4 border-l-green-500",
        warning: "border-l-4 border-l-amber-500",
        critical: "border-l-4 border-l-red-500",
        unknown: "border-l-4 border-l-gray-400",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "md",
      status: "normal",
    },
  }
)

export interface StatusCardMetric {
  label: string
  value: string | number
  unit?: string
  trend?: "up" | "down" | "stable"
  trendValue?: number
  trendUnit?: string
  status?: "normal" | "warning" | "critical"
}

export interface StatusCardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof statusCardVariants> {
  /** Card title */
  title: string
  /** System or component status */
  status: "normal" | "warning" | "critical" | "unknown"
  /** Status description */
  description?: string
  /** Last updated timestamp */
  lastUpdated?: Date | string
  /** Show last updated time */
  showLastUpdated?: boolean
  /** Primary metrics to display */
  metrics?: StatusCardMetric[]
  /** Secondary information */
  secondaryInfo?: string
  /** Loading state */
  loading?: boolean
  /** Interactive - shows hover effects */
  interactive?: boolean
  /** Click handler for interactive cards */
  onClick?: () => void
  /** Show actions menu */
  showActions?: boolean
  /** Actions menu callback */
  onActionsClick?: () => void
  /** Refresh callback */
  onRefresh?: () => void
  /** Show refresh button */
  showRefresh?: boolean
  /** Auto-refresh interval in seconds */
  autoRefreshInterval?: number
  /** Custom status icon */
  statusIcon?: LucideIcon
  /** Test identifier for automated testing */
  "data-testid"?: string
}

export const StatusCard = React.forwardRef<HTMLDivElement, StatusCardProps>(
  (
    {
      variant = "default",
      size = "md",
      title,
      status,
      description,
      lastUpdated,
      showLastUpdated = true,
      metrics = [],
      secondaryInfo,
      loading = false,
      interactive = false,
      onClick,
      showActions = false,
      onActionsClick,
      onRefresh,
      showRefresh = false,
      autoRefreshInterval,
      statusIcon,
      className,
      "data-testid": testId,
      ...props
    },
    ref
  ) => {
    const [isRefreshing, setIsRefreshing] = React.useState(false)
    const refreshIntervalRef = React.useRef<NodeJS.Timeout | null>(null)
    
    // Status indicator variant mapping
    const statusVariantMap = {
      normal: "operational" as const,
      warning: "warning" as const,
      critical: "critical" as const,
      unknown: "offline" as const,
    }
    
    // Format last updated time
    const formatLastUpdated = React.useCallback((timestamp: Date | string) => {
      const date = timestamp instanceof Date ? timestamp : new Date(timestamp)
      const now = new Date()
      const diff = Math.floor((now.getTime() - date.getTime()) / 1000)
      
      if (diff < 60) return "Just now"
      if (diff < 3600) return `${Math.floor(diff / 60)}m ago`
      if (diff < 86400) return `${Math.floor(diff / 3600)}h ago`
      return `${Math.floor(diff / 86400)}d ago`
    }, [])
    
    // Handle refresh
    const handleRefresh = React.useCallback(async () => {
      if (!onRefresh || isRefreshing) return
      
      setIsRefreshing(true)
      try {
        await onRefresh()
      } finally {
        setTimeout(() => setIsRefreshing(false), 1000)
      }
    }, [onRefresh, isRefreshing])
    
    // Auto-refresh setup
    React.useEffect(() => {
      if (autoRefreshInterval && onRefresh) {
        refreshIntervalRef.current = setInterval(() => {
          if (!isRefreshing) {
            handleRefresh()
          }
        }, autoRefreshInterval * 1000)
        
        return () => {
          if (refreshIntervalRef.current) {
            clearInterval(refreshIntervalRef.current)
          }
        }
      }
    }, [autoRefreshInterval, onRefresh, handleRefresh, isRefreshing])
    
    // Get trend icon
    const getTrendIcon = (trend: "up" | "down" | "stable", trendStatus?: string) => {
      const iconClass = cn(
        "h-3 w-3",
        trendStatus === "critical" ? "text-red-500" :
        trendStatus === "warning" ? "text-amber-500" :
        trend === "up" ? "text-green-500" :
        trend === "down" ? "text-red-500" :
        "text-gray-500"
      )
      
      switch (trend) {
        case "up":
          return <TrendingUp className={iconClass} />
        case "down":
          return <TrendingDown className={iconClass} />
        default:
          return <Activity className={iconClass} />
      }
    }
    
    return (
      <div
        ref={ref}
        className={cn(
          statusCardVariants({ variant, size, status }),
          interactive && "cursor-pointer hover:bg-accent/50",
          loading && "animate-pulse",
          className
        )}
        onClick={interactive ? onClick : undefined}
        data-testid={testId || `status-card-${title.toLowerCase().replace(/\s+/g, "-")}`}
        role={interactive ? "button" : undefined}
        tabIndex={interactive ? 0 : undefined}
        {...props}
      >
        {/* Header */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center gap-2 flex-1 min-w-0">
            <StatusIndicator
              variant={statusVariantMap[status]}
              size="sm"
              showIcon
              icon={statusIcon}
              animate={loading || isRefreshing}
            />
            <div className="flex-1 min-w-0">
              <h3 className={cn(
                "font-semibold text-foreground truncate",
                size === "sm" ? "text-sm" : size === "lg" ? "text-lg" : "text-base"
              )}>
                {title}
              </h3>
              {description && (
                <p className="text-sm text-muted-foreground mt-0.5">
                  {description}
                </p>
              )}
            </div>
          </div>
          
          {/* Actions */}
          <div className="flex items-center gap-1 ml-2">
            {showRefresh && onRefresh && (
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  handleRefresh()
                }}
                disabled={isRefreshing}
                className="h-6 w-6 p-0"
                aria-label="Refresh status"
              >
                <RefreshCw className={cn("h-3 w-3", isRefreshing && "animate-spin")} />
              </Button>
            )}
            
            {showActions && onActionsClick && (
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  onActionsClick()
                }}
                className="h-6 w-6 p-0"
                aria-label="More actions"
              >
                <MoreHorizontal className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>
        
        {/* Metrics */}
        {metrics.length > 0 && (
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-3">
            {metrics.map((metric, index) => (
              <div key={index} className="space-y-1">
                <div className="flex items-center justify-between text-xs">
                  <span className="text-muted-foreground">{metric.label}</span>
                  {metric.trend && (
                    <div className="flex items-center gap-1">
                      {getTrendIcon(metric.trend, metric.status)}
                      {metric.trendValue && (
                        <span className={cn(
                          "text-xs",
                          metric.status === "critical" ? "text-red-500" :
                          metric.status === "warning" ? "text-amber-500" :
                          "text-muted-foreground"
                        )}>
                          {metric.trend === "up" ? "+" : metric.trend === "down" ? "-" : ""}
                          {metric.trendValue}
                          {metric.trendUnit}
                        </span>
                      )}
                    </div>
                  )}
                </div>
                <div className="flex items-baseline gap-1">
                  <span className={cn(
                    "font-mono font-semibold",
                    size === "sm" ? "text-lg" : size === "lg" ? "text-2xl" : "text-xl",
                    metric.status === "critical" ? "text-red-600" :
                    metric.status === "warning" ? "text-amber-600" :
                    "text-foreground"
                  )}>
                    {metric.value}
                  </span>
                  {metric.unit && (
                    <span className="text-sm text-muted-foreground">
                      {metric.unit}
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
        
        {/* Secondary info and timestamps */}
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          {secondaryInfo && (
            <span>{secondaryInfo}</span>
          )}
          
          {showLastUpdated && lastUpdated && (
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              <span>{formatLastUpdated(lastUpdated)}</span>
            </div>
          )}
        </div>
      </div>
    )
  }
)

StatusCard.displayName = "StatusCard"

// Convenience components for electrical systems
export const PowerSystemStatus = React.forwardRef<HTMLDivElement, 
  Omit<StatusCardProps, "title"> & { systemName?: string }
>(({ systemName = "Power System", ...props }, ref) => (
  <StatusCard ref={ref} title={systemName} {...props} />
))
PowerSystemStatus.displayName = "PowerSystemStatus"

export const MotorStatus = React.forwardRef<HTMLDivElement, 
  Omit<StatusCardProps, "title"> & { motorName?: string }
>(({ motorName = "Motor", ...props }, ref) => (
  <StatusCard ref={ref} title={motorName} {...props} />
))
MotorStatus.displayName = "MotorStatus"

export const TransformerStatus = React.forwardRef<HTMLDivElement, 
  Omit<StatusCardProps, "title"> & { transformerName?: string }
>(({ transformerName = "Transformer", ...props }, ref) => (
  <StatusCard ref={ref} title={transformerName} {...props} />
))
TransformerStatus.displayName = "TransformerStatus"

export const PanelStatus = React.forwardRef<HTMLDivElement, 
  Omit<StatusCardProps, "title"> & { panelName?: string }
>(({ panelName = "Electrical Panel", ...props }, ref) => (
  <StatusCard ref={ref} title={panelName} {...props} />
))
PanelStatus.displayName = "PanelStatus"

// Export types for external use
export type StatusCardVariant = NonNullable<StatusCardProps["variant"]>
export type StatusCardSize = NonNullable<StatusCardProps["size"]>
export type StatusCardStatus = NonNullable<StatusCardProps["status"]>

// Export variants for external use
export { statusCardVariants }

// Utility functions for electrical systems
export const createElectricalStatusMetrics = (
  systemType: "power" | "motor" | "transformer" | "panel",
  values?: Partial<Record<string, number>>
): StatusCardMetric[] => {
  const baseMetrics = [
    {
      label: "Voltage",
      value: values?.voltage || 480,
      unit: "V",
      trend: "stable" as const,
      status: "normal" as const,
    },
    {
      label: "Current", 
      value: values?.current || 45,
      unit: "A",
      trend: "stable" as const,
      status: "normal" as const,
    },
  ]
  
  const systemSpecific: Record<string, StatusCardMetric[]> = {
    power: [
      ...baseMetrics,
      {
        label: "Power",
        value: values?.power || 21.6,
        unit: "kW", 
        trend: "up" as const,
        trendValue: 2.1,
        trendUnit: "%",
        status: "normal" as const,
      },
      {
        label: "PF",
        value: values?.powerFactor || 0.92,
        unit: "",
        trend: "stable" as const,
        status: "normal" as const,
      },
    ],
    motor: [
      ...baseMetrics,
      {
        label: "Speed",
        value: values?.speed || 1785,
        unit: "RPM",
        trend: "stable" as const,
        status: "normal" as const,
      },
      {
        label: "Temp",
        value: values?.temperature || 68,
        unit: "°C",
        trend: "up" as const,
        trendValue: 3,
        trendUnit: "°C",
        status: values?.temperature && values.temperature > 75 ? "warning" : "normal" as const,
      },
    ],
    transformer: [
      ...baseMetrics,
      {
        label: "Load",
        value: values?.load || 67,
        unit: "%",
        trend: "up" as const,
        trendValue: 5,
        trendUnit: "%",
        status: values?.load && values.load > 80 ? "warning" : "normal" as const,
      },
      {
        label: "Oil Temp",
        value: values?.oilTemp || 55,
        unit: "°C",
        trend: "stable" as const,
        status: "normal" as const,
      },
    ],
    panel: baseMetrics,
  }
  
  return systemSpecific[systemType] || baseMetrics
}

export const determineSystemStatus = (metrics: StatusCardMetric[]): StatusCardStatus => {
  if (metrics.some(m => m.status === "critical")) return "critical"
  if (metrics.some(m => m.status === "warning")) return "warning" 
  return "normal"
}