# Atomic Design Implementation Guide

## Overview

This guide provides comprehensive documentation for the atomic design components implemented in the Ultimate Electrical Designer application. All components follow atomic design principles with electrical engineering domain specialization.

## Architecture Principles

### Atomic Design Hierarchy

1. **Atoms** (Foundational): Basic building blocks that cannot be broken down further
2. **Molecules** (Composed): Combinations of atoms that form reusable component groups
3. **Organisms** (Complex): Combinations of molecules and atoms that form distinct interface sections
4. **Templates** (Layout): Page-level compositions that demonstrate content structure
5. **Pages** (Complete): Specific instances of templates with real content

### Electrical Engineering Integration

All components include specialized variants and configurations for electrical engineering contexts:

- **Professional Status Management**: Operational, warning, critical, fault states
- **Electrical Role Integration**: Project Manager, Lead Engineer, Electrical Engineer roles
- **Industry Standards Compliance**: Color coding and terminology per IEEE/IEC standards
- **Domain-Specific Workflows**: Equipment monitoring, system health, component management

## Atoms (Foundational Building Blocks)

### Button
Basic interactive element with electrical engineering variants.

```tsx
import { Button } from '@/components/atoms'

// Basic usage
<Button variant="default">Standard Action</Button>
<Button variant="electrical" size="lg">System Control</Button>

// Electrical engineering contexts
<Button variant="danger" size="sm">Emergency Stop</Button>
<Button variant="success">System Operational</Button>
```

**Variants:** `default`, `destructive`, `outline`, `secondary`, `ghost`, `link`, `electrical`
**Sizes:** `default`, `sm`, `lg`, `icon`

### Input
Form input component with validation states and electrical contexts.

```tsx
import { Input } from '@/components/atoms'

// Basic usage
<Input placeholder="Enter voltage value" />
<Input variant="electrical" state="warning" />

// With validation states
<Input state="error" placeholder="Invalid current reading" />
<Input state="success" placeholder="Acceptable range" />
```

**Variants:** `default`, `outline`, `filled`, `electrical`
**States:** `default`, `error`, `success`, `warning`

### Label
Text labels with electrical engineering typography standards.

```tsx
import { Label } from '@/components/atoms'

// Basic usage
<Label>Voltage Rating</Label>
<Label size="lg" weight="semibold">Critical Parameters</Label>

// Electrical contexts
<Label state="warning">Over Current Protection</Label>
<Label size="sm" weight="normal">Equipment ID</Label>
```

**Sizes:** `sm`, `default`, `lg`
**Weights:** `normal`, `medium`, `semibold`, `bold`
**States:** `default`, `error`, `success`, `warning`

### Icon
Scalable vector icons with electrical engineering icon set.

```tsx
import { Icon } from '@/components/atoms'

// Basic usage
<Icon name="power" size="md" />
<Icon name="alert-triangle" color="warning" />

// Electrical contexts
<Icon name="zap" size="lg" color="electrical" />
<Icon name="settings" size="sm" color="muted" />
```

**Available Icons:** `power`, `zap`, `alert-triangle`, `check`, `x`, `settings`, `info`, `user`, `users`, `calendar`, `clock`, `search`, `filter`, `download`, `upload`, `edit`, `trash`, `eye`, `eye-off`, `chevron-down`, `chevron-up`, `chevron-left`, `chevron-right`

### Badge
Status and category indicators for electrical systems.

```tsx
import { Badge } from '@/components/atoms'

// Basic usage
<Badge>Active</Badge>
<Badge variant="secondary">Standby</Badge>

// Electrical contexts
<Badge variant="destructive">Fault</Badge>
<Badge variant="outline" size="lg">480V</Badge>
```

**Variants:** `default`, `secondary`, `destructive`, `outline`
**Sizes:** `sm`, `default`, `lg`

### StatusIndicator
Professional status indicators for electrical systems and equipment.

```tsx
import { StatusIndicator } from '@/components/atoms'

// Basic system status
<StatusIndicator status="operational" />
<StatusIndicator status="warning" size="lg" />

// Electrical engineering contexts
<StatusIndicator status="energized" style="led" />
<StatusIndicator status="fault" size="sm" style="dot" />

// Custom electrical systems
<StatusIndicator 
  status="de_energized" 
  size="md" 
  style="icon"
  className="mr-2" 
/>
```

**Electrical Statuses:** `energized`, `de_energized`, `fault`, `maintenance`, `testing`, `commissioning`
**System Statuses:** `operational`, `warning`, `critical`, `offline`
**Styles:** `dot`, `icon`, `led`

### Avatar
User representation with electrical engineering role indicators.

```tsx
import { Avatar, AvatarGroup } from '@/components/atoms'

// Basic user avatar
<Avatar 
  src="/user-photo.jpg" 
  alt="John Smith" 
  fallback="JS" 
/>

// Electrical engineering roles
<Avatar 
  role="Lead Engineer"
  status="online"
  size="lg"
  fallback="LE"
/>

// Group of electrical team members
<AvatarGroup max={4} size="sm">
  <Avatar role="Project Manager" fallback="PM" />
  <Avatar role="Electrical Engineer" fallback="EE" />
  <Avatar role="CAD Operator" fallback="CO" />
  <Avatar role="Instrumentation Engineer" fallback="IE" />
</AvatarGroup>
```

**Electrical Roles:** `Project Manager`, `Lead Engineer`, `Electrical Engineer`, `Automation Engineer`, `Instrumentation Engineer`, `CAD Operator`

### ProgressBar & CircularProgress
Progress indicators for electrical system processes and calculations.

```tsx
import { ProgressBar, CircularProgress } from '@/components/atoms'

// Linear progress for calculations
<ProgressBar 
  value={75} 
  variant="electrical" 
  size="lg"
  animated
/>

// Circular progress for system loading
<CircularProgress 
  value={60} 
  size="lg"
  showValue
  className="text-electrical"
/>

// Electrical engineering contexts
<ProgressBar 
  value={85}
  variant="warning" // Over 80% capacity
  size="md"
  animated
/>
```

**Variants:** `default`, `success`, `warning`, `danger`, `electrical`
**Features:** Animation, value display, electrical color coding

### Chip
Removable tags and filters for electrical component categorization.

```tsx
import { Chip, ChipGroup } from '@/components/atoms'

// Basic component tags
<Chip>480V</Chip>
<Chip variant="electrical" onRemove={() => {}}>
  Motor Control
</Chip>

// Electrical component groups
<ChipGroup>
  <Chip variant="outline">Switchgear</Chip>
  <Chip variant="filled">Panel Board</Chip>
  <Chip variant="electrical">VFD</Chip>
</ChipGroup>

// Electrical status chips
{createElectricalStatusChips([
  'energized', 'operational', 'testing'
]).map(chip => (
  <Chip key={chip.status} variant={chip.variant}>
    {chip.label}
  </Chip>
))}
```

**Electrical Variants:** Auto-generated based on equipment status and type

## Molecules (Composed Building Blocks)

### InputField
Complete form field combining Label, Input, and validation feedback.

```tsx
import { InputField } from '@/components/molecules'

// Basic form field
<InputField
  label="Voltage Rating"
  placeholder="Enter voltage in volts"
  required
/>

// Electrical engineering validation
<InputField
  label="Current Rating"
  type="number"
  state="error"
  helperText="Value must be between 0-1000A"
  variant="electrical"
/>
```

### ButtonGroup
Grouped button actions for electrical system controls.

```tsx
import { ButtonGroup, ActionButtonGroup } from '@/components/molecules'

// Basic button group
<ButtonGroup
  buttons={[
    { id: 'start', label: 'Start', variant: 'success' },
    { id: 'stop', label: 'Stop', variant: 'danger' },
    { id: 'reset', label: 'Reset', variant: 'outline' }
  ]}
  onChange={(id) => handleSystemControl(id)}
/>

// Electrical control actions
<ActionButtonGroup
  actions={[
    { id: 'energize', label: 'Energize', variant: 'electrical', icon: 'zap' },
    { id: 'isolate', label: 'Isolate', variant: 'outline', icon: 'x' },
    { id: 'test', label: 'Test', variant: 'secondary', icon: 'settings' }
  ]}
  onAction={handleElectricalAction}
/>
```

### AlertCard
Professional alert cards for electrical system notifications.

```tsx
import { AlertCard, ElectricalAlertCard, FaultAlertCard } from '@/components/molecules'

// Basic system alerts
<AlertCard
  variant="warning"
  title="High Temperature Detected"
  description="Motor temperature exceeds normal operating range"
  dismissible
  onDismiss={() => acknowledgeAlert()}
/>

// Electrical system alerts
<ElectricalAlertCard
  title="Over Current Protection Activated"
  description="Circuit breaker CB-101 has tripped due to overcurrent condition"
  equipmentId="CB-101"
  timestamp={new Date()}
/>

// Fault notifications
<FaultAlertCard
  title="Ground Fault Detected"
  description="GFCI protection activated on Panel PNL-2A"
  severity="critical"
  location="Electrical Room A"
/>
```

### HealthIndicator
Comprehensive system health monitoring for electrical equipment.

```tsx
import { HealthIndicator, PowerSystemHealth, MotorHealth } from '@/components/molecules'

// Basic health monitoring
<HealthIndicator
  status="healthy"
  metrics={[
    { name: 'Temperature', value: 65, unit: '°C', status: 'normal' },
    { name: 'Vibration', value: 2.1, unit: 'mm/s', status: 'warning' },
    { name: 'Current', value: 45.2, unit: 'A', status: 'normal' }
  ]}
/>

// Specialized electrical system health
<PowerSystemHealth
  voltage={480}
  current={125.5}
  power={75.2}
  powerFactor={0.85}
  frequency={60}
/>

<MotorHealth
  temperature={72}
  vibration={1.8}
  current={32.1}
  efficiency={92.5}
  runtime={1250}
/>
```

### SearchBox
Advanced search functionality for electrical components and systems.

```tsx
import { SearchBox, ComponentSearchBox, EquipmentSearchBox } from '@/components/molecules'

// Basic search
<SearchBox
  placeholder="Search electrical components..."
  onSearch={(query, filters) => handleSearch(query, filters)}
  filters={[
    { id: 'voltage', label: 'Voltage Rating', type: 'select', options: ['120V', '240V', '480V'] },
    { id: 'type', label: 'Component Type', type: 'checkbox', options: ['Motor', 'Panel', 'Breaker'] }
  ]}
/>

// Specialized component search
<ComponentSearchBox
  onSearch={handleComponentSearch}
  categories={['Switchgear', 'Motor Control', 'Panel Board']}
  voltageRatings={['120V', '240V', '480V', '600V']}
/>

// Equipment-specific search
<EquipmentSearchBox
  onSearch={handleEquipmentSearch}
  locations={['Electrical Room A', 'MCC Room', 'Utility Building']}
  systems={['Power Distribution', 'Motor Control', 'Lighting']}
/>
```

### StatusCard
Comprehensive status display cards for electrical systems and equipment.

```tsx
import { StatusCard, PowerSystemStatus, MotorStatus } from '@/components/molecules'

// General system status
<StatusCard
  title="Main Distribution Panel"
  status="operational"
  metrics={[
    { name: 'Load', value: 75, unit: '%', status: 'normal' },
    { name: 'Temperature', value: 45, unit: '°C', status: 'normal' },
    { name: 'Voltage', value: 480, unit: 'V', status: 'normal' }
  ]}
  location="Electrical Room A"
  lastUpdated={new Date()}
/>

// Power system monitoring
<PowerSystemStatus
  panelId="MDP-01"
  voltage={480}
  current={325.7}
  power={275.4}
  load={68}
  temperature={42}
  status="operational"
/>

// Motor status monitoring
<MotorStatus
  motorId="MTR-101"
  speed={1750}
  current={28.5}
  power={15.2}
  temperature={68}
  vibration={1.2}
  efficiency={91.5}
  status="operational"
/>
```

## Usage Patterns

### Component Composition
Combine atoms to create molecules, and molecules to create organisms:

```tsx
// Molecule: Motor Control Panel
function MotorControlPanel({ motorId, status, onControl }) {
  return (
    <StatusCard
      title={`Motor ${motorId}`}
      status={status}
      actions={
        <ButtonGroup
          buttons={[
            { id: 'start', label: 'Start', variant: 'success' },
            { id: 'stop', label: 'Stop', variant: 'danger' }
          ]}
          onChange={onControl}
        />
      }
    >
      <HealthIndicator
        status={status}
        metrics={motorMetrics}
      />
    </StatusCard>
  )
}
```

### Electrical Engineering Contexts
Use electrical-specific variants and configurations:

```tsx
// Electrical system dashboard
function ElectricalSystemDashboard() {
  return (
    <div className="space-y-6">
      <PowerSystemHealth {...powerMetrics} />
      
      <SearchBox
        placeholder="Search electrical equipment..."
        filters={createElectricalSearchFilters()}
      />
      
      <div className="grid grid-cols-2 gap-4">
        <MotorStatus {...motor1Status} />
        <MotorStatus {...motor2Status} />
      </div>
      
      <AlertCard
        variant="electrical"
        title="Maintenance Due"
        description="Scheduled maintenance required for Panel PNL-2A"
      />
    </div>
  )
}
```

### Accessibility Integration
All components include WCAG 2.1 AA compliance:

```tsx
// Accessible electrical system controls
<ButtonGroup
  buttons={[
    { 
      id: 'energize', 
      label: 'Energize System',
      variant: 'electrical',
      'aria-describedby': 'energize-help'
    }
  ]}
/>
<p id="energize-help" className="sr-only">
  Energizes the electrical system. Use caution.
</p>
```

## Migration Guide

### Legacy Component Replacement

Replace legacy components with atomic equivalents:

```tsx
// Before: Legacy component
<LegacyButton type="primary" size="large">
  Submit
</LegacyButton>

// After: Atomic component
<Button variant="default" size="lg">
  Submit
</Button>

// Before: Legacy status display
<StatusDisplay status="active" color="green" />

// After: Atomic status indicator
<StatusIndicator status="operational" size="md" />
```

### Electrical Domain Migration

Upgrade to electrical engineering-specific variants:

```tsx
// Before: Generic status
<Badge variant="success">Online</Badge>

// After: Electrical status
<StatusIndicator status="energized" style="led" />

// Before: Generic user display
<UserAvatar name="John Smith" role="Engineer" />

// After: Electrical role avatar
<Avatar 
  role="Electrical Engineer" 
  status="online"
  fallback="EE"
/>
```

## Best Practices

### 1. Consistent Styling
Always use provided variants and avoid custom styling:

```tsx
// Good
<Button variant="electrical" size="lg">Control System</Button>

// Avoid
<Button className="bg-blue-500 text-white px-8 py-4">Control System</Button>
```

### 2. Electrical Context
Use electrical-specific variants for domain contexts:

```tsx
// Good - electrical engineering context
<StatusIndicator status="energized" style="led" />
<Avatar role="Lead Engineer" />

// Less ideal - generic context
<Badge variant="success">On</Badge>
<Avatar />
```

### 3. Accessibility First
Always provide proper accessibility attributes:

```tsx
// Good
<Button aria-describedby="help-text">
  Emergency Stop
</Button>
<p id="help-text" className="sr-only">
  Immediately stops all system operations
</p>

// Missing accessibility
<Button>Emergency Stop</Button>
```

### 4. Composition Over Inheritance
Build complex interfaces by composing atoms and molecules:

```tsx
// Good - composition
<StatusCard>
  <HealthIndicator />
  <ButtonGroup />
</StatusCard>

// Avoid - monolithic components
<ComplexElectricalPanel />
```

## Next Steps

### Phase 3: Organisms Implementation
The next implementation phase will create organism-level components:

- **Equipment Dashboard**: Complete monitoring interfaces
- **Control Panels**: System operation interfaces  
- **Project Navigation**: Full project management UI
- **System Configuration**: Settings and setup interfaces

### Phase 4: Templates & Pages
Complete page-level implementations combining all atomic design levels.

## Support

For questions or issues with atomic design components:

1. Check component TypeScript definitions for available props
2. Review test files for usage examples
3. Consult electrical engineering domain specifications
4. Follow atomic design principles for composition

All components maintain backward compatibility and can be imported individually or as groups from their respective index files.