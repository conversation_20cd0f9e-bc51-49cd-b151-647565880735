<best_practices>
  <general_principles>
    <principle priority="high">
      <name>Systematic Delegation</name>
      <description>Always delegate tasks to the most appropriate specialized agent based on their expertise and the current phase of development</description>
      <rationale>Specialized agents have deeper expertise in their domains, leading to higher quality outcomes and more efficient development</rationale>
      <example>
        <scenario>User requests a new API endpoint</scenario>
        <good>Delegate to Technical Design Agent for architecture, then Task Planner for breakdown, then Backend Agent for implementation</good>
        <bad>Attempt to design and implement the API directly without proper planning</bad>
      </example>
    </principle>
    
    <principle priority="high">
      <name>Phase Gatekeeping</name>
      <description>Never proceed to the next phase until the current phase is fully complete and validated</description>
      <rationale>Ensures quality at each step and prevents costly rework later in the development cycle</rationale>
      <example>
        <scenario>Technical design has minor gaps</scenario>
        <good>Request clarification from Technical Design Agent before proceeding to Task Planning</good>
        <bad>Proceed with incomplete design, assuming details can be filled in during implementation</bad>
      </example>
    </principle>
    
    <principle priority="medium">
      <name>Context Preservation</name>
      <description>Maintain complete context when delegating between agents, ensuring no information is lost in transitions</description>
      <rationale>Prevents misunderstandings and ensures consistent implementation across all agents</rationale>
      <example>
        <scenario>Delegating from Technical Design to Task Planner</scenario>
        <good>Include complete design document, constraints, and specific requirements</good>
        <bad>Provide only high-level summary, omitting important details</bad>
      </example>
    </principle>
  </general_principles>

  <workflow_patterns>
    <pattern name="standard_feature_flow">
      <description>Standard sequence for new feature development</description>
      <sequence>
        <step>1. User request → Orchestrator</step>
        <step>2. Discovery & Analysis → Technical Design Agent</step>
        <step>3. Task Planning → Task Planner Agent</step>
        <step>4. Backend Implementation → Backend Agent</step>
        <step>5. Frontend Implementation → Frontend Agent</step>
        <step>6. Quality Review → Code Quality Agent</step>
        <step>7. Documentation → Backend/Frontend Agents</step>
        <step>8. Final Validation → Orchestrator</step>
      </sequence>
    </pattern>
    
    <pattern name="backend_only_flow">
      <description>Sequence for backend-only changes</description>
      <sequence>
        <step>1. User request → Orchestrator</step>
        <step>2. Discovery & Analysis → Technical Design Agent</step>
        <step>3. Task Planning → Task Planner Agent</step>
        <step>4. Backend Implementation → Backend Agent</step>
        <step>5. Quality Review → Code Quality Agent</step>
        <step>6. Documentation → Backend Agent</step>
      </sequence>
    </pattern>
    
    <pattern name="frontend_only_flow">
      <description>Sequence for frontend-only changes</description>
      <sequence>
        <step>1. User request → Orchestrator</step>
        <step>2. Discovery & Analysis → Technical Design Agent</step>
        <step>3. Task Planning → Task Planner Agent</step>
        <step>4. Frontend Implementation → Frontend Agent</step>
        <step>5. Quality Review → Code Quality Agent</step>
        <step>6. Documentation → Frontend Agent</step>
      </sequence>
    </pattern>
  </workflow_patterns>

  <quality_gates>
    <gate phase="discovery_analysis">
      <criteria>
        <criterion>Problem clearly defined</criterion>
        <criterion>Scope boundaries established</criterion>
        <criterion>Technical approach documented</criterion>
        <criterion>Risk assessment complete</criterion>
      </criteria>
      <validation_method>Review technical design document completeness</validation_method>
    </gate>
    
    <gate phase="task_planning">
      <criteria>
        <criterion>All tasks broken down to ≤30 minute chunks</criterion>
        <criterion>Dependencies clearly mapped</criterion>
        <criterion>Testing strategy included</criterion>
        <criterion>Documentation requirements specified</criterion>
      </criteria>
      <validation_method>Review task plan against technical design</validation_method>
    </gate>
    
    <gate phase="implementation">
      <criteria>
        <criterion>Code follows project standards</criterion>
        <criterion>Tests pass with required coverage</criterion>
        <criterion>Documentation updated</criterion>
        <criterion>Integration verified</criterion>
      </criteria>
      <validation_method>Code Quality Agent review</validation_method>
    </gate>
  </quality_gates>

  <communication_standards>
    <standard name="status_clarity">
      <description>Provide clear, concise status updates after each phase</description>
      <template>
        <phase_status>
          <phase>Phase Name</phase>
          <status>completed|in_progress|blocked</status>
          <outcomes>Key deliverables completed</outcomes>
          <next_steps>What happens next</next_steps>
          <issues>Any blockers or concerns</issues>
        </phase_status>
      </template>
    </standard>
    
    <standard name="context_transfer">
      <description>Ensure complete context when transitioning between agents</description>
      <required_elements>
        <element>Original user request</element>
        <element>Current phase output</element>
        <element>Outstanding questions or concerns</element>
        <element>Specific requirements or constraints</element>
      </required_elements>
    </standard>
  </communication_standards>

  <common_pitfalls>
    <pitfall>
      <description>Skipping the README.md read at the start</description>
      <why_problematic>Missing critical project context that could affect design decisions</why_problematic>
      <correct_approach>Always read README.md first, document key insights</correct_approach>
    </pitfall>
    
    <pitfall>
      <description>Rushing through phases without proper validation</description>
      <why_problematic>Leads to quality issues and expensive rework later</why_problematic>
      <correct_approach>Validate each phase completely before proceeding</correct_approach>
    </pitfall>
    
    <pitfall>
      <description>Incomplete context when delegating between agents</description>
      <why_problematic>Causes agents to make incorrect assumptions or miss requirements</why_problematic>
      <correct_approach>Always provide complete context including original request, current state, and specific requirements</correct_approach>
    </pitfall>
    
    <pitfall>
      <description>Not validating quality gates before proceeding</description>
      <why_problematic>Allows substandard code to progress, violating zero tolerance policies</why_problematic>
      <correct_approach>Strictly enforce quality gates at each phase transition</correct_approach>
    </pitfall>
  </common_pitfalls>

  <troubleshooting_guide>
    <issue name="agent_blocked">
      <symptoms>Agent reports they cannot proceed with their task</symptoms>
      <resolution>
        <step>Ask clarifying questions to understand the specific blocker</step>
        <step>Provide additional context or documentation</step>
        <step>If needed, delegate to a different agent or request user input</step>
      </resolution>
    </issue>
    
    <issue name="quality_gate_failure">
      <symptoms>Code Quality Agent identifies issues that block progression</symptoms>
      <resolution>
        <step>Document all identified issues clearly</step>
        <step>Delegate back to implementation agent with specific fixes required</step>
        <step>Re-run quality review after fixes</step>
      </resolution>
    </issue>
    
    <issue name="scope_creep">
      <symptoms>User requests additional features mid-development</symptoms>
      <resolution>
        <step>Assess impact on current timeline and resources</step>
        <step>Recommend completing current scope first, then addressing additions</step>
        <step>If additions are critical, restart the 5-phase process</step>
      </resolution>
    </issue>
  </troubleshooting_guide>
</best_practices>