<examples>
  <example name="complete_feature_development">
    <description>End-to-end example of developing a new user authentication feature</description>
    <context>
      <user_request>Implement JWT-based authentication system with refresh tokens</user_request>
      <project_state>Existing FastAPI backend with user model, Next.js frontend</project_state>
    </context>
    
    <workflow_execution>
      <phase name="discovery_analysis">
        <orchestrator_action>
          <step>Read README.md to understand current auth state</step>
          <step>Delegate to Technical Design Agent</step>
        </orchestrator_action>
        <delegation>
          <new_task>
            <mode>architect</mode>
            <message>Create technical design for JWT authentication system with refresh tokens</message>
          </new_task>
        </delegation>
        <technical_design_output>
          <components>
            <component>JWT token generation service</component>
            <component>Refresh token mechanism</component>
            <component>Login/logout endpoints</component>
            <component>Token refresh endpoint</component>
            <component>Frontend auth context</component>
          </components>
          <security_considerations>
            <item>Token expiration times</item>
            <item>Refresh token rotation</item>
            <item>Secure cookie storage</item>
          </security_considerations>
        </technical_design_output>
      </phase>
      
      <phase name="task_planning">
        <orchestrator_action>
          <step>Review technical design completeness</step>
          <step>Delegate to Task Planner Agent</step>
        </orchestrator_action>
        <delegation>
          <new_task>
            <mode>code</mode>
            <message>Create detailed task plan for JWT authentication implementation</message>
          </new_task>
        </delegation>
        <task_plan_output>
          <tasks>
            <task id="1">Create JWT service with token generation</task>
            <task id="2">Implement refresh token storage</task>
            <task id="3">Create login endpoint</task>
            <task id="4">Create logout endpoint</task>
            <task id="5">Create token refresh endpoint</task>
            <task id="6">Create frontend auth context</task>
            <task id="7">Create login/logout UI components</task>
            <task id="8">Add tests for all components</task>
          </tasks>
        </task_plan_output>
      </phase>
      
      <phase name="implementation">
        <orchestrator_action>
          <step>Delegate backend tasks to Backend Agent</step>
          <step>Delegate frontend tasks to Frontend Agent</step>
          <step>Coordinate integration</step>
        </orchestrator_action>
        <backend_delegation>
          <new_task>
            <mode>code</mode>
            <message>Implement JWT service and authentication endpoints</message>
          </new_task>
        </backend_delegation>
        <frontend_delegation>
          <new_task>
            <mode>code</mode>
            <message>Implement frontend authentication context and components</message>
          </new_task>
        </frontend_delegation>
      </phase>
      
      <phase name="verification">
        <orchestrator_action>
          <step>Delegate comprehensive quality review</step>
        </orchestrator_action>
        <quality_delegation>
          <new_task>
            <mode>debug</mode>
            <message>Review JWT authentication implementation for security and quality</message>
          </new_task>
        </quality_delegation>
      </phase>
      
      <phase name="documentation">
        <orchestrator_action>
          <step>Verify all documentation is complete</step>
          <step>Confirm feature readiness</step>
        </orchestrator_action>
      </phase>
    </workflow_execution>
  </example>
  
  <example name="backend_only_api_endpoint">
    <description>Adding a new API endpoint for project management</description>
    <context>
      <user_request>Add CRUD endpoints for managing electrical projects</user_request>
      <project_state>Existing FastAPI backend with project model</project_state>
    </context>
    
    <workflow_execution>
      <phase name="discovery_analysis">
        <orchestrator_action>
          <step>Check existing project model and requirements</step>
          <step>Delegate technical design</step>
        </orchestrator_action>
        <delegation>
          <new_task>
            <mode>architect</mode>
            <message>Design CRUD endpoints for electrical project management</message>
          </new_task>
        </delegation>
      </phase>
      
      <phase name="task_planning">
        <orchestrator_action>
          <step>Delegate task breakdown</step>
        </orchestrator_action>
        <delegation>
          <new_task>
            <mode>code</mode>
            <message>Create task plan for project CRUD endpoints</message>
          </new_task>
        </delegation>
      </phase>
      
      <phase name="implementation">
        <orchestrator_action>
          <step>Delegate backend implementation only</step>
        </orchestrator_action>
        <delegation>
          <new_task>
            <mode>code</mode>
            <message>Implement project CRUD endpoints using CRUD factory pattern</message>
          </new_task>
        </delegation>
      </phase>
      
      <phase name="verification">
        <orchestrator_action>
          <step>Delegate quality review</step>
        </orchestrator_action>
        <delegation>
          <new_task>
            <mode>debug</mode>
            <message>Review project CRUD endpoint implementation</message>
          </new_task>
        </delegation>
      </phase>
    </workflow_execution>
  </example>
  
  <example name="handling_quality_issues">
    <description>How to handle when Code Quality Agent finds issues</description>
    <context>
      <scenario>Code Quality Agent reports security vulnerabilities in JWT implementation</scenario>
    </context>
    
    <resolution_process>
      <step number="1">
        <action>Document all identified issues</action>
        <details>
          Create clear list of security vulnerabilities with severity levels
        </details>
      </step>
      
      <step number="2">
        <action>Prioritize issues</action>
        <details>
          Focus on high-severity security issues first
        </details>
      </step>
      
      <step number="3">
        <action>Delegate fixes</action>
        <details>
          <new_task>
            <mode>code</mode>
            <message>Fix JWT security vulnerabilities: [specific issues]</message>
          </new_task>
        </details>
      </step>
      
      <step number="4">
        <action>Re-verify</action>
        <details>
          Run quality review again after fixes are implemented
        </details>
      </step>
      
      <step number="5">
        <action>Escalate if needed</action>
        <details>
          If issues persist, ask user for guidance on approach
        </details>
      </step>
    </resolution_process>
  </example>
  
  <example name="scope_change_management">
    <description>Handling user requests for additional features mid-development</description>
    <context>
      <scenario>During JWT auth implementation, user requests OAuth2 integration</scenario>
    </context>
    
    <resolution_process>
      <step number="1">
        <action>Assess impact</action>
        <details>
          Evaluate OAuth2 complexity and timeline impact on current JWT work
        </details>
      </step>
      
      <step number="2">
        <action>Recommend approach</action>
        <details>
          Suggest completing JWT implementation first, then adding OAuth2 as separate feature
        </details>
      </step>
      
      <step number="3">
        <action>Document decision</action>
        <details>
          Record scope change and new timeline for user approval
        </details>
      </step>
      
      <step number="4">
        <action>Proceed accordingly</action>
        <details>
          Either continue with original scope or restart 5-phase process for expanded scope
        </details>
      </step>
    </resolution_process>
  </example>
  
  <example name="error_scenario_handling">
    <description>Handling when an agent reports they cannot complete their task</description>
    <context>
      <scenario>Backend Agent reports they cannot implement JWT due to missing dependencies</scenario>
    </context>
    
    <resolution_process>
      <step number="1">
        <action>Clarify the issue</action>
        <details>
          <ask_followup_question>
            <question>What specific dependencies are missing for JWT implementation?</question>
            <follow_up>
              <suggest>PyJWT library needs to be added to requirements</suggest>
              <suggest>Environment variables for JWT secret are not configured</suggest>
              <suggest>Database schema needs updates for refresh tokens</suggest>
            </follow_up>
          </ask_followup_question>
        </details>
      </step>
      
      <step number="2">
        <action>Address the blocker</action>
        <details>
          Based on clarification, either:
          - Delegate dependency installation
          - Update environment configuration
          - Modify database schema
        </details>
      </step>
      
      <step number="3">
        <action>Resume workflow</action>
        <details>
          Once blocker is resolved, continue with original delegation
        </details>
      </step>
    </resolution_process>
  </example>
</examples>