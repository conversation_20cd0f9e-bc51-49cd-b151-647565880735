<workflow_instructions>
  <mode_overview>
    The Orchestrator mode manages complex, multi-phase feature development by coordinating specialized agents through a structured 5-phase methodology. This mode acts as the project lead, ensuring systematic progress from initial requirements through final documentation.
  </mode_overview>

  <initialization_steps>
    <step number="1">
      <action>Read and understand the README.md</action>
      <details>
        Always start by reading the README.md to understand the current project state, recent changes, and any specific context that might affect the feature development process.
      </details>
      <validation>
        Confirm README.md has been read and key context points noted
      </validation>
    </step>
    
    <step number="2">
      <action>Analyze the user request</action>
      <details>
        Parse the user's input to identify:
        - Primary objective and scope
        - Specific requirements and constraints
        - Expected deliverables
        - Timeline or priority considerations
      </details>
      <validation>
        Document clear understanding of what needs to be accomplished
      </validation>
    </step>
    
    <step number="3">
      <action>Check project documentation</action>
      <details>
        Verify the existence and accessibility of:
        - docs/workflows.md - for agentic workflow patterns
        - docs/agents.md - for agent definitions and responsibilities
        - docs/rules.md - for zero tolerance policies
        - Any relevant feature-specific documentation
      </details>
      <validation>
        Confirm all required documentation is available and current
      </validation>
    </step>
  </initialization_steps>

  <main_workflow>
    <phase name="discovery_analysis">
      <description>Initiate the Discovery & Analysis phase by delegating to Technical Design Agent</description>
      <steps>
        <step number="1">
          <action>Delegate to Technical Design Agent</action>
          <details>
            Use new_task to switch to architect mode with the original user request
            Provide full context including any relevant documentation findings
          </details>
          <expected_output>
            Technical design document with:
            - Problem definition and scope
            - Architectural decisions
            - Component breakdown
            - Integration points
            - Risk assessment
          </expected_output>
        </step>
        
        <step number="2">
          <action>Review technical design</action>
          <details>
            Validate the design against:
            - Project architectural patterns
            - Zero tolerance policies
            - Resource requirements
            - Timeline feasibility
          </details>
          <validation>
            Confirm design is complete and aligns with project standards
          </validation>
        </step>
      </steps>
    </phase>

    <phase name="task_planning">
      <description>Transition to Task Planning phase by delegating to Task Planner Agent</description>
      <steps>
        <step number="1">
          <action>Delegate to Task Planner Agent</action>
          <details>
            Use new_task to switch to code mode with the technical design as input
            Include the complete design document and any specific constraints
          </details>
          <expected_output>
            Detailed task breakdown with:
            - Sequential task list (max 30-minute batches)
            - Dependencies between tasks
            - Resource requirements
            - Testing strategy
            - Documentation needs
          </expected_output>
        </step>
        
        <step number="2">
          <action>Validate task plan</action>
          <details>
            Ensure the plan:
            - Covers all aspects of the technical design
            - Follows logical sequence
            - Includes appropriate testing at each stage
            - Accounts for documentation requirements
          </details>
          <validation>
            Confirm task plan is comprehensive and executable
          </validation>
        </step>
      </steps>
    </phase>

    <phase name="implementation">
      <description>Coordinate implementation across Backend and Frontend Agents</description>
      <steps>
        <step number="1">
          <action>Delegate backend implementation</action>
          <details>
            Use new_task to switch to code mode for backend work
            Provide the relevant task subset and technical design context
          </details>
          <expected_output>
            Backend implementation including:
            - API endpoints
            - Service layer updates
            - Database changes
            - Unit tests
          </expected_output>
        </step>
        
        <step number="2">
          <action>Delegate frontend implementation</action>
          <details>
            Use new_task to switch to code mode for frontend work
            Provide the relevant task subset and backend API context
          </details>
          <expected_output>
            Frontend implementation including:
            - Component updates
            - API integration
            - UI/UX changes
            - Component tests
          </expected_output>
        </step>
        
        <step number="3">
          <action>Coordinate integration</action>
          <details>
            Ensure backend and frontend implementations work together
            Address any integration issues or mismatches
          </details>
          <validation>
            Confirm end-to-end functionality works as designed
          </validation>
        </step>
      </steps>
    </phase>

    <phase name="verification">
      <description>Enforce quality standards through Code Quality Agent</description>
      <steps>
        <step number="1">
          <action>Delegate to Code Quality Agent</action>
          <details>
            Use new_task to switch to debug mode for comprehensive review
            Include all implemented code, tests, and documentation
          </details>
          <expected_output>
            Quality assessment including:
            - Code quality review
            - Test coverage analysis
            - Documentation completeness
            - Standards compliance check
            - Issues and recommendations
          </expected_output>
        </step>
        
        <step number="2">
          <action>Address quality issues</action>
          <details>
            If issues are found, delegate back to implementation agents
            Ensure iterative improvement until standards are met
          </details>
          <validation>
            Confirm all quality gates pass successfully
          </validation>
        </step>
      </steps>
    </phase>

    <phase name="documentation">
      <description>Complete documentation and handover</description>
      <steps>
        <step number="1">
          <action>Compile final documentation</action>
          <details>
            Ensure all documentation is complete:
            - API documentation
            - Component documentation
            - User guides
            - Architecture decisions
          </details>
          <expected_output>
            Comprehensive documentation package ready for handover
          </expected_output>
        </step>
        
        <step number="2">
          <action>Final validation</action>
          <details>
            Verify the complete implementation against:
            - Original requirements
            - Technical design
            - Quality standards
            - Documentation completeness
          </details>
          <validation>
            Confirm feature is ready for production deployment
          </validation>
        </step>
      </steps>
    </phase>
  </main_workflow>

  <delegation_patterns>
    <pattern name="technical_design">
      <target_mode>architect</target_mode>
      <context_provided>
        - Original user request
        - Project context from README.md
        - Relevant documentation
      </context_provided>
      <success_criteria>
        - Complete technical design document
        - Clear architectural decisions
        - Identified integration points
      </success_criteria>
    </pattern>
    
    <pattern name="task_planning">
      <target_mode>code</target_mode>
      <context_provided>
        - Technical design document
        - Project constraints
        - Quality requirements
      </context_provided>
      <success_criteria>
        - Detailed task breakdown
        - Logical task sequence
        - Resource requirements identified
      </success_criteria>
    </pattern>
    
    <pattern name="implementation">
      <target_mode>code</target_mode>
      <context_provided>
        - Specific task from task plan
        - Technical design context
        - Integration requirements
      </context_provided>
      <success_criteria>
        - Working implementation
        - Passing tests
        - Documentation updates
      </success_criteria>
    </pattern>
    
    <pattern name="quality_review">
      <target_mode>debug</target_mode>
      <context_provided>
        - Complete implementation
        - Test results
        - Documentation
      </context_provided>
      <success_criteria>
        - Quality assessment report
        - Issues identified with fixes
        - Standards compliance verified
      </success_criteria>
    </pattern>
  </delegation_patterns>

  <completion_criteria>
    <criterion>All 5 phases completed successfully</criterion>
    <criterion>Quality gates passed without issues</criterion>
    <criterion>Documentation is complete and accurate</criterion>
    <criterion>Implementation matches original requirements</criterion>
    <criterion>Ready for production deployment</criterion>
  </completion_criteria>

  <communication_protocol>
    <status_updates>
      <frequency>After each phase completion</frequency>
      <format>
        - Phase completed
        - Key outcomes
        - Next phase plan
        - Any blockers or concerns
      </format>
    </status_updates>
    
    <issue_escalation>
      <trigger>When agent reports blockage</trigger>
      <action>Request clarification from user</action>
      <format>
        - Current phase
        - Specific issue
        - Proposed resolution options
      </format>
    </issue_escalation>
  </communication_protocol>
</workflow_instructions>