<error_handling_communication>
  <error_categories>
    <category name="agent_unavailable">
      <description>When a specialized agent mode is not available</description>
      <symptoms>
        <symptom>new_task fails with "mode not found" error</symptom>
        <symptom>Agent reports they cannot handle the specific task type</symptom>
      </symptoms>
      <resolution_steps>
        <step priority="1">
          <action>Check available modes</action>
          <details>Use list_files to see what modes are actually available</details>
        </step>
        <step priority="2">
          <action>Fallback delegation</action>
          <details>Route to the closest available mode (e.g., code mode for implementation tasks)</details>
        </step>
        <step priority="3">
          <action>Inform user</action>
          <details>Clearly communicate the limitation and adjusted approach</details>
        </step>
      </resolution_steps>
    </category>
    
    <category name="incomplete_requirements">
      <description>When user request lacks sufficient detail</description>
      <symptoms>
        <symptom>Technical Design Agent cannot create complete design</symptom>
        <symptom>Multiple interpretations of requirements possible</symptom>
      </symptoms>
      <resolution_steps>
        <step priority="1">
          <action>Ask clarifying questions</action>
          <details>Use ask_followup_question to get specific details</details>
        </step>
        <step priority="2">
          <action>Document assumptions</action>
          <details>Clearly state any assumptions being made</details>
        </step>
        <step priority="3">
          <action>Get user confirmation</action>
          <details>Confirm approach before proceeding with implementation</details>
        </step>
      </resolution_steps>
    </category>
    
    <category name="quality_gate_failures">
      <description>When Code Quality Agent identifies blocking issues</description>
      <symptoms>
        <symptom>Tests failing below required coverage</symptom>
        <symptom>Security vulnerabilities identified</symptom>
        <symptom>Code standards violations</symptom>
      </symptoms>
      <resolution_steps>
        <step priority="1">
          <action>Document all issues</action>
          <details>Create comprehensive list with severity levels</details>
        </step>
        <step priority="2">
          <action>Prioritize fixes</action>
          <details>Focus on high-severity issues first</details>
        </step>
        <step priority="3">
          <action>Delegate fixes</action>
          <details>Route specific fixes to appropriate implementation agent</details>
        </step>
        <step priority="4">
          <action>Re-verify</action>
          <details>Run quality review again after fixes</details>
        </step>
        <step priority="5">
          <action>Escalate if needed</action>
          <details>Inform user if issues cannot be resolved within constraints</details>
        </step>
      </resolution_steps>
    </category>
    
    <category name="integration_issues">
      <description>When backend and frontend implementations don't align</description>
      <symptoms>
        <symptom>API endpoints don't match frontend expectations</symptom>
        <symptom>Data format mismatches</symptom>
        <symptom>Authentication flow breaks</symptom>
      </symptoms>
      <resolution_steps>
        <step priority="1">
          <action>Identify specific mismatches</action>
          <details>Compare API contracts with frontend usage</details>
        </step>
        <step priority="2">
          <action>Determine root cause</action>
          <details>Check if issue is in design, implementation, or communication</details>
        </step>
        <step priority="3">
          <action>Coordinate fixes</action>
          <details>May need to update both backend and frontend</details>
        </step>
        <step priority="4">
          <action>Re-test integration</action>
          <details>Verify end-to-end functionality</details>
        </step>
      </resolution_steps>
    </category>
  </error_categories>

  <communication_protocols>
    <protocol name="status_updates">
      <frequency>After each phase completion</frequency>
      <format>
        <status_message>
          <phase_completed>[Phase Name]</phase_completed>
          <summary>[Brief summary of what was accomplished]</summary>
          <next_phase>[What's coming next]</next_phase>
          <timeline>[Estimated time for next phase]</timeline>
          <issues>[Any concerns or blockers]</issues>
        </status_message>
      </format>
      <example>
        <status_message>
          <phase_completed>Discovery & Analysis</phase_completed>
          <summary>Technical design completed for JWT authentication system</summary>
          <next_phase>Task Planning</next_phase>
          <timeline>15-20 minutes</timeline>
          <issues>None - design is comprehensive and ready for planning</issues>
        </status_message>
      </example>
    </protocol>
    
    <protocol name="issue_escalation">
      <trigger>When progress is blocked for more than 5 minutes</trigger>
      <format>
        <escalation_message>
          <current_phase>[Current phase]</current_phase>
          <blocker>[Specific issue preventing progress]</blocker>
          <impact>[How this affects timeline/deliverables]</impact>
          <options>[Proposed resolution options]</options>
          <user_action>[What user needs to decide/provide]</user_action>
        </escalation_message>
      </format>
      <example>
        <escalation_message>
          <current_phase>Implementation</current_phase>
          <blocker>PyJWT library not available in project dependencies</blocker>
          <impact>Cannot proceed with JWT token generation</impact>
          <options>1) Add PyJWT to requirements.txt, 2) Use alternative JWT library</options>
          <user_action>Please confirm which approach to take</user_action>
        </escalation_message>
      </example>
    </protocol>
    
    <protocol name="scope_change">
      <trigger>User requests changes to original requirements</trigger>
      <format>
        <scope_change_message>
          <original_scope>[What was originally requested]</original_scope>
          <requested_change>[What user now wants]</requested_change>
          <impact_assessment>
            <timeline>[How this affects delivery timeline]</timeline>
            <complexity>[Additional complexity introduced]</complexity>
            <dependencies>[New dependencies or requirements]</dependencies>
          </impact_assessment>
          <recommendation>[Suggested approach]</recommendation>
          <user_decision>[What user needs to decide]</user_decision>
        </scope_change_message>
      </format>
      <example>
        <scope_change_message>
          <original_scope>JWT authentication with email/password</original_scope>
          <requested_change>Add OAuth2 integration with Google and GitHub</requested_oauth_providers>
          <impact_assessment>
            <timeline>Additional 2-3 hours for OAuth2 implementation</timeline>
            <complexity>Requires OAuth2 flow implementation and provider integrations</complexity>
            <dependencies>Need OAuth2 client credentials for each provider</dependencies>
          </impact_assessment>
          <recommendation>Complete JWT email/password auth first, then add OAuth2 as separate feature</recommendation>
          <user_decision>Proceed with original scope or restart with expanded scope?</user_decision>
        </scope_change_message>
      </example>
    </protocol>
  </communication_protocols>

  <recovery_procedures>
    <procedure name="workflow_restart">
      <trigger>Major scope change or fundamental design flaw discovered</trigger>
      <steps>
        <step>
          <action>Document current state</action>
          <details>Record what has been completed and what needs to change</details>
        </step>
        <step>
          <action>Assess impact</action>
          <details>Determine how much work needs to be redone</details>
        </step>
        <step>
          <action>Get user confirmation</action>
          <details>Confirm restart is the right approach</details>
        </step>
        <step>
          <action>Restart 5-phase process</action>
          <details>Begin again from Discovery & Analysis with new requirements</details>
        </step>
      </steps>
    </procedure>
    
    <procedure name="partial_rollback">
      <trigger>Quality issues in specific component</trigger>
      <steps>
        <step>
          <action>Identify affected components</action>
          <details>Determine which parts need to be reworked</details>
        </step>
        <step>
          <action>Preserve working components</action>
          <details>Keep components that meet quality standards</details>
        </step>
        <step>
          <action>Delegate fixes</action>
          <details>Route specific fixes to appropriate agent</details>
        </step>
        <step>
          <action>Re-verify specific components</action>
          <details>Focus quality review on fixed components</details>
        </step>
      </steps>
    </procedure>
  </recovery_procedures>

  <decision_tree>
    <decision name="how_to_proceed_with_issues">
      <question>What type of issue is blocking progress?</question>
      <branches>
        <branch condition="Agent unavailable">
          <action>Use fallback mode or ask user for alternative</action>
        </branch>
        <branch condition="Incomplete requirements">
          <action>Ask clarifying questions and get user input</action>
        </branch>
        <branch condition="Quality issues">
          <action>Delegate fixes and re-verify</action>
        </branch>
        <branch condition="Integration problems">
          <action>Coordinate between agents to resolve</action>
        </branch>
        <branch condition="Scope change">
          <action>Assess impact and get user decision</action>
        </branch>
      </branches>
    </decision>
  </decision_tree>

  <escalation_matrix>
    <level name="agent_level">
      <duration>0-5 minutes</duration>
      <resolution>Handle within agent delegation</resolution>
    </level>
    
    <level name="orchestrator_level">
      <duration>5-15 minutes</duration>
      <resolution>Coordinate between agents or adjust workflow</resolution>
    </level>
    
    <level name="user_level">
      <duration>15+ minutes</duration>
      <resolution>Ask user for clarification or decision</resolution>
    </level>
  </escalation_matrix>
</error_handling_communication>