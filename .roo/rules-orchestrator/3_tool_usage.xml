<tool_usage_guide>
  <tool_priorities>
    <priority level="1">
      <tool>read_file</tool>
      <when>Always use first to read README.md and project documentation</when>
      <why>Establishes project context and ensures decisions align with current state</why>
    </priority>
    
    <priority level="2">
      <tool>new_task</tool>
      <when>Delegating to specialized agents for each phase</when>
      <why>Properly routes work to agents with appropriate expertise</why>
    </priority>
    
    <priority level="3">
      <tool>ask_followup_question</tool>
      <when>Need clarification from user or when agents report blockages</when>
      <why>Ensures clear understanding before proceeding, prevents incorrect assumptions</why>
    </priority>
    
    <priority level="4">
      <tool>list_files</tool>
      <when>Need to understand project structure or locate specific files</when>
      <why>Provides context for delegation decisions and ensures agents have necessary information</why>
    </priority>
  </tool_priorities>

  <delegation_patterns>
    <pattern name="technical_design_delegation">
      <tool>new_task</tool>
      <parameters>
        <parameter name="mode" required="true">architect</parameter>
        <parameter name="message" required="true">Complete technical design for: [user_request]</parameter>
      </parameters>
      <context_template>
        <context>
          <original_request>[Full user request]</original_request>
          <project_context>[Key points from README.md]</project_context>
          <constraints>[Any specific constraints identified]</constraints>
          <deliverables>[Expected outputs]</deliverables>
        </context>
      </context_template>
      <example>
        <code>
<new_task>
<mode>architect</mode>
<message>Create technical design for implementing user authentication with JWT tokens</message>
</new_task>
        </code>
      </example>
    </pattern>
    
    <pattern name="task_planning_delegation">
      <tool>new_task</tool>
      <parameters>
        <parameter name="mode" required="true">code</parameter>
        <parameter name="message" required="true">Create detailed task plan based on technical design</parameter>
      </parameters>
      <context_template>
        <context>
          <technical_design>[Complete design document]</technical_design>
          <scope>[Specific components to plan]</scope>
          <constraints>[Time/resource constraints]</constraints>
          <quality_requirements>[Testing and documentation needs]</quality_requirements>
        </context>
      </context_template>
    </pattern>
    
    <pattern name="implementation_delegation">
      <tool>new_task</tool>
      <parameters>
        <parameter name="mode" required="true">code</parameter>
        <parameter name="message" required="true">Implement [specific task] according to plan</parameter>
      </parameters>
      <context_template>
        <context>
          <task_details>[Specific task from plan]</task_details>
          <technical_context>[Relevant design information]</technical_context>
          <integration_points>[Dependencies and interfaces]</integration_points>
          <testing_requirements>[What needs to be tested]</testing_requirements>
        </context>
      </context_template>
    </pattern>
    
    <pattern name="quality_review_delegation">
      <tool>new_task</tool>
      <parameters>
        <parameter name="mode" required="true">debug</parameter>
        <parameter name="message" required="true">Perform comprehensive quality review</parameter>
      </parameters>
      <context_template>
        <context>
          <implementation>[All implemented code]</implementation>
          <tests>[Test files and results]</tests>
          <documentation>[Updated documentation]</documentation>
          <standards>[Specific standards to check]</standards>
        </context>
      </context_template>
    </pattern>
  </delegation_patterns>

  <status_tracking>
    <method name="phase_completion">
      <description>Track when each phase is successfully completed</description>
      <template>
        <phase_completion>
          <phase_name>[Discovery|Planning|Implementation|Verification|Documentation]</phase_name>
          <completion_status>completed</completion_status>
          <key_deliverables>[List of completed items]</key_deliverables>
          <quality_verified>true</quality_verified>
          <ready_for_next_phase>true</ready_for_next_phase>
        </phase_completion>
      </template>
    </method>
    
    <method name="progress_reporting">
      <description>Provide regular progress updates to user</description>
      <template>
        <progress_update>
          <current_phase>[Current phase name]</current_phase>
          <overall_progress>[X/5 phases completed]</overall_progress>
          <recent_completion>[What was just completed]</recent_completion>
          <next_action>[What happens next]</next_action>
          <estimated_remaining>[Time/effort estimate]</estimated_remaining>
        </progress_update>
      </template>
    </method>
  </status_tracking>

  <error_handling>
    <scenario name="agent_unavailable">
      <symptoms>Target agent mode is not available or fails to respond</symptoms>
      <resolution>
        <step>Check available modes with list_files</step>
        <step>Fallback to closest available mode</step>
        <step>Inform user of mode limitation</step>
        <step>Adjust workflow accordingly</step>
      </resolution>
    </scenario>
    
    <scenario name="incomplete_design">
      <symptoms>Technical design is missing critical elements</symptoms>
      <resolution>
        <step>Identify specific missing elements</step>
        <step>Ask_followup_question to clarify requirements</step>
        <step>Delegate back to Technical Design Agent with specific questions</step>
        <step>Do not proceed until design is complete</step>
      </resolution>
    </scenario>
    
    <scenario name="quality_gate_failure">
      <symptoms>Code Quality Agent identifies blocking issues</symptoms>
      <resolution>
        <step>Document all identified issues</step>
        <step>Prioritize issues by severity</step>
        <step>Delegate fixes to appropriate implementation agent</step>
        <step>Re-verify after fixes</step>
        <step>Escalate to user if issues cannot be resolved</step>
      </resolution>
    </scenario>
  </error_handling>

  <validation_checklists>
    <checklist name="before_delegation">
      <item>All required context is provided</item>
      <item>Previous phase output is complete</item>
      <item>Quality gates are met</item>
      <item>User expectations are clear</item>
    </checklist>
    
    <checklist name="phase_completion">
      <item>All deliverables are present</item>
      <item>Quality standards are met</item>
      <item>Documentation is updated</item>
      <item>Next phase requirements are clear</item>
    </checklist>
    
    <checklist name="project_completion">
      <item>All 5 phases completed successfully</item>
      <item>Quality gates passed without issues</item>
      <item>Documentation is complete and accurate</item>
      <item>Implementation matches original requirements</item>
      <item>Ready for production deployment</item>
    </checklist>
  </validation_checklists>
</tool_usage_guide>