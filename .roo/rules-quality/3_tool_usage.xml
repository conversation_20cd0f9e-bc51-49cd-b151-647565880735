<tool_usage_guide>
  <tool_priorities>
    <priority level="1">
      <tool>execute_command</tool>
      <when>Run all quality checks and tests</when>
      <why>Primary method for executing quality verification</why>
    </priority>
    
    <priority level="2">
      <tool>read_file</tool>
      <when>Review configuration files and reports</when>
      <why>Understand project setup and analyze results</why>
    </priority>
    
    <priority level="3">
      <tool>search_files</tool>
      <when>Find specific violations or patterns</when>
      <why>Locate issues across the codebase</why>
    </priority>
  </tool_priorities>

  <backend_quality_tools>
    <tool name="mypy">
      <purpose>Python type checking</purpose>
      <commands>
        <command>cd server && uv run mypy src/ --show-error-codes</command>
        <command>cd server && uv run mypy src/ --strict --show-error-codes</command>
        <command>cd server && uv run mypy src/modules/[module]/ --show-error-codes</command>
      </commands>
      <output_parsing>
        <pattern>error: (.*?):(\d+): error: (.*)</pattern>
        <fields>
          <field name="file">path</field>
          <field name="line">line_number</field>
          <field name="message">error_message</field>
        </fields>
      </output_parsing>
      <acceptable_results>
        <result>Zero errors</result>
      </acceptable_results>
    </tool>
    
    <tool name="ruff">
      <purpose>Python linting and formatting</purpose>
      <commands>
        <command>cd server && uv run ruff check . --output-format=json</command>
        <command>cd server && uv run ruff format . --check</command>
        <command>cd server && uv run ruff check src/ --select=ALL --ignore=S101,ANN101,ANN102</command>
      </commands>
      <output_parsing>
        <pattern>{"code": "(.*?)", "message": "(.*?)", "filename": "(.*?)", "line": (\d+)}</pattern>
        <fields>
          <field name="rule">code</field>
          <field name="message">message</field>
          <field name="file">filename</field>
          <field name="line">line</field>
        </fields>
      </output_parsing>
      <acceptable_results>
        <result>Zero warnings</result>
      </acceptable_results>
    </tool>
    
    <tool name="pytest">
      <purpose>Python testing framework</purpose>
      <commands>
        <command>cd server && uv run pytest tests/ -v --tb=short</command>
        <command>cd server && uv run pytest tests/ --cov=src --cov-report=term-missing --cov-fail-under=85</command>
        <command>cd server && uv run pytest tests/ --junitxml=test-results.xml</command>
      </commands>
      <output_parsing>
        <pattern>FAILED (.*?) - (.*)</pattern>
        <fields>
          <field name="test_name">test_identifier</field>
          <field name="failure">error_message</field>
        </fields>
      </output_parsing>
      <acceptable_results>
        <result>All tests passed</result>
      </acceptable_results>
    </tool>
    
    <tool name="bandit">
      <purpose>Python security analysis</purpose>
      <commands>
        <command>cd server && uv run bandit -r src/ -f json -o bandit-report.json</command>
        <command>cd server && uv run bandit -r src/ -f txt</command>
      </commands>
      <output_parsing>
        <pattern>"issue_severity": "(HIGH|MEDIUM)"</pattern>
        <fields>
          <field name="severity">issue_severity</field>
        </fields>
      </output_parsing>
      <acceptable_results>
        <result>Zero HIGH/MEDIUM severity issues</result>
      </acceptable_results>
    </tool>
  </backend_quality_tools>

  <frontend_quality_tools>
    <tool name="typescript">
      <purpose>TypeScript type checking</purpose>
      <commands>
        <command>cd client && pnpm tsc --noEmit</command>
        <command>cd client && pnpm tsc --noEmit --project tsconfig.json</command>
        <command>cd client && pnpm tsc --noEmit --incremental false</command>
      </commands>
      <output_parsing>
        <pattern>(.*?)\((\d+),(\d+)\): error (.*?): (.*)</pattern>
        <fields>
          <field name="file">path</field>
          <field name="line">line_number</field>
          <field name="column">column_number</field>
          <field name="code">error_code</field>
          <field name="message">error_message</field>
        </fields>
      </output_parsing>
      <acceptable_results>
        <result>Zero errors</result>
      </acceptable_results>
    </tool>
    
    <tool name="eslint">
      <purpose>JavaScript/TypeScript linting</purpose>
      <commands>
        <command>cd client && pnpm next lint --format=json</command>
        <command>cd client && pnpm next lint --dir src/components</command>
        <command>cd client && pnpm next lint --max-warnings=0</command>
      </commands>
      <output_parsing>
        <pattern>{"filePath": "(.*?)", "messages": \[{"line": (\d+), "message": "(.*?)", "ruleId": "(.*?)"}\]</pattern>
        <fields>
          <field name="file">filePath</field>
          <field name="line">line</field>
          <field name="message">message</field>
          <field name="rule">ruleId</field>
        </fields>
      </output_parsing>
      <acceptable_results>
        <result>Zero warnings</result>
      </acceptable_results>
    </tool>
    
    <tool name="prettier">
      <purpose>Code formatting verification</purpose>
      <commands>
        <command>cd client && pnpm prettier --check "src/**/*.{ts,tsx,js,jsx,json,md}"</command>
        <command>cd client && pnpm prettier --list-different "src/**/*.{ts,tsx,js,jsx}"</command>
      </commands>
      <output_parsing>
        <pattern>(.*?)</pattern>
        <fields>
          <field name="file">path</field>
        </fields>
      </output_parsing>
      <acceptable_results>
        <result>All files properly formatted</result>
      </acceptable_results>
    </tool>
    
    <tool name="vitest">
      <purpose>Frontend testing framework</purpose>
      <commands>
        <command>cd client && pnpm vitest --run --reporter=json --outputFile=vitest-results.json</command>
        <command>cd client && pnpm vitest --run --coverage --reporter=verbose</command>
      </commands>
      <output_parsing>
        <pattern>"numFailedTests": (\d+)</pattern>
        <fields>
          <field name="failed_count">numFailedTests</field>
        </fields>
      </output_parsing>
      <acceptable_results>
        <result>Zero failed tests</result>
      </acceptable_results>
    </tool>
    
    <tool name="playwright">
      <purpose>End-to-end testing</purpose>
      <commands>
        <command>cd client && pnpm playwright test --reporter=json</command>
        <command>cd client && pnpm playwright test --reporter=html</command>
      </commands>
      <output_parsing>
        <pattern>(\d+) failed</pattern>
        <fields>
          <field name="failed_count">failed_tests</field>
        </fields>
      </output_parsing>
      <acceptable_results>
        <result>Zero failed tests</result>
      </acceptable_results>
    </tool>
  </frontend_quality_tools>

  <report_generation>
    <template name="quality_report">
      <format>
        <![CDATA[
# Code Quality Verification Report

**Status**: [PASS/FAIL]
**Timestamp**: [ISO 8601]
**Scope**: [Backend/Frontend/Full Stack]

## Summary
- **Total Checks**: [count]
- **Passed**: [count]
- **Failed**: [count]
- **Success Rate**: [percentage]

## Detailed Results

### Backend Quality
- **MyPy**: [PASS/FAIL] - [error count] errors
- **Ruff**: [PASS/FAIL] - [warning count] warnings
- **Pytest**: [PASS/FAIL] - [pass/fail count]
- **Coverage**: [PASS/FAIL] - [coverage%] (target: 85%)
- **Security**: [PASS/FAIL] - [issue count] issues

### Frontend Quality
- **TypeScript**: [PASS/FAIL] - [error count] errors
- **ESLint**: [PASS/FAIL] - [warning count] warnings
- **Prettier**: [PASS/FAIL] - [formatting issues]
- **Vitest**: [PASS/FAIL] - [pass/fail count]
- **Playwright**: [PASS/FAIL] - [pass/fail count]

## Violations

### [Tool Name]
**File**: [path]
**Line**: [number]
**Rule**: [rule name]
**Message**: [error message]
**Severity**: [HIGH/MEDIUM/LOW]

## Recommendations
[Specific actions to resolve violations]

## Next Steps
1. [Action 1]
2. [Action 2]
3. [Action 3]
        ]]>
      </format>
    </template>
  </report_generation>

  <automation_scripts>
    <script name="full_verification">
      <description>Complete quality verification script</description>
      <commands>
        <![CDATA[
#!/bin/bash
# Backend verification
cd server
echo "=== Backend Quality Checks ==="
echo "Running MyPy..."
uv run mypy src/ --show-error-codes
MYPY_STATUS=$?

echo "Running Ruff..."
uv run ruff check .
RUFF_STATUS=$?

echo "Running tests..."
uv run pytest tests/ --cov=src --cov-report=term-missing --cov-fail-under=85
PYTEST_STATUS=$?

# Frontend verification
cd ../client
echo "=== Frontend Quality Checks ==="
echo "Running TypeScript..."
pnpm tsc --noEmit
TS_STATUS=$?

echo "Running ESLint..."
pnpm next lint --max-warnings=0
ESLINT_STATUS=$?

echo "Running Prettier..."
pnpm prettier --check "src/**/*.{ts,tsx}"
PRETTIER_STATUS=$?

echo "Running tests..."
pnpm vitest --run --coverage
VITEST_STATUS=$?

echo "Running E2E tests..."
pnpm playwright test
PLAYWRIGHT_STATUS=$?

# Generate report
echo "=== Quality Report ==="
echo "MyPy: $MYPY_STATUS"
echo "Ruff: $RUFF_STATUS"
echo "Pytest: $PYTEST_STATUS"
echo "TypeScript: $TS_STATUS"
echo "ESLint: $ESLINT_STATUS"
echo "Prettier: $PRETTIER_STATUS"
echo "Vitest: $VITEST_STATUS"
echo "Playwright: $PLAYWRIGHT_STATUS"
        ]]>
      </commands>
    </script>
  </automation_scripts>

  <troubleshooting>
    <issue name="environment_setup">
      <symptoms>Tools not found or configuration issues</symptoms>
      <solutions>
        <solution>Verify virtual environment is activated</solution>
        <solution>Check tool installation status</solution>
        <solution>Validate configuration files</solution>
      </solutions>
    </issue>
    
    <issue name="performance_issues">
      <symptoms>Slow test execution or quality checks</symptoms>
      <solutions>
        <solution>Use parallel test execution</solution>
        <solution>Implement incremental checking</solution>
        <solution>Cache results where appropriate</solution>
      </solutions>
    </issue>
  </troubleshooting>
</tool_usage_guide>