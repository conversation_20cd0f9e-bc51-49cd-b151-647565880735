<error_handling>
  <error_categories>
    <category name="tool_execution_failures">
      <description>Issues with quality tool execution</description>
      <scenarios>
        <scenario>
          <symptom>MyPy crashes or hangs</symptom>
          <diagnosis>
            <step>Check for circular imports</step>
            <step>Verify mypy configuration</step>
            <step>Check for memory issues</step>
          </diagnosis>
          <resolution>
            <action>Restart with incremental mode disabled</action>
            <action>Check mypy.ini configuration</action>
            <action>Run on smaller code subsets</action>
          </resolution>
        </scenario>
        
        <scenario>
          <symptom>Test suite timeout</symptom>
          <diagnosis>
            <step>Check for hanging async tests</step>
            <step>Verify test database setup</step>
            <step>Check for infinite loops</step>
          </diagnosis>
          <resolution>
            <action>Add timeout configurations</action>
            <action>Check test fixtures</action>
            <action>Run tests in smaller batches</action>
          </resolution>
        </scenario>
      </scenarios>
    </category>
    
    <category name="configuration_issues">
      <description>Problems with quality tool configuration</description>
      <scenarios>
        <scenario>
          <symptom>Configuration file not found</symptom>
          <diagnosis>
            <step>Check file paths</step>
            <step>Verify file permissions</step>
            <step>Check for typos in filenames</step>
          </diagnosis>
          <resolution>
            <action>Restore missing configuration files</action>
            <action>Verify project structure</action>
            <action>Recreate configuration from templates</action>
          </resolution>
        </scenario>
        
        <scenario>
          <symptom>Inconsistent results between environments</symptom>
          <diagnosis>
            <step>Check tool versions</step>
            <step>Verify environment variables</step>
            <step>Check for OS-specific issues</step>
          </diagnosis>
          <resolution>
            <action>Pin tool versions</action>
            <action>Standardize environment setup</action>
            <action>Use containerized environments</action>
          </resolution>
        </scenario>
      </scenarios>
    </category>
    
    <category name="false_positives">
      <description>Quality tools reporting false issues</description>
      <scenarios>
        <scenario>
          <symptom>MyPy reports false positive</symptom>
          <diagnosis>
            <step>Check mypy version</step>
            <step>Verify type stubs</step>
            <step>Check for known bugs</step>
          </diagnosis>
          <resolution>
            <action>Update mypy and stubs</action>
            <action>Add type: ignore with justification</action>
            <action>Report upstream bug</action>
          </resolution>
        </scenario>
        
        <scenario>
          <symptom>ESLint false positive</symptom>
          <diagnosis>
            <step>Check rule configuration</step>
            <step>Verify plugin versions</step>
            <step>Check for rule conflicts</step>
          </diagnosis>
          <resolution>
            <action>Adjust rule configuration</action>
            <action>Add eslint-disable with justification</action>
            <action>Update ESLint version</action>
          </resolution>
        </scenario>
      </scenarios>
    </category>
  </error_categories>

  <recovery_procedures>
    <procedure name="tool_recovery">
      <steps>
        <step>Identify failing tool</step>
        <step>Check tool logs</step>
        <step>Verify configuration</step>
        <step>Restart with minimal config</step>
        <step>Gradually add complexity</step>
      </steps>
    </procedure>
    
    <procedure name="environment_reset">
      <steps>
        <step>Backup current state</step>
        <step>Clean environment</step>
        <step>Reinstall dependencies</step>
        <step>Restore configuration</step>
        <step>Verify functionality</step>
      </steps>
    </procedure>
  </recovery_procedures>

  <escalation_paths>
    <path name="tool_malfunction">
      <steps>
        <step>Document the issue</step>
        <step>Check for known issues</step>
        <step>Escalate to development team</step>
        <step>Provide workaround if possible</step>
      </steps>
    </path>
    
    <path name="configuration_dispute">
      <steps>
        <step>Reference documentation</step>
        <step>Provide clear justification</step>
        <step>Escalate to Technical Design Agent</step>
        <step>Document resolution</step>
      </steps>
    </path>
  </escalation_paths>
</error_handling>