<communication_guidelines>
  <interaction_patterns>
    <pattern name="verification_request">
      <description>How to handle verification requests</description>
      <format>
        <![CDATA[
## Quality Verification Request

**Feature**: [feature name]
**Scope**: [backend/frontend/both]
**Changes**: [brief description]
**Branch**: [branch name]
**Commit**: [commit hash]

**Ready for verification**: [yes/no]
        ]]>
      </format>
    </pattern>
    
    <pattern name="verification_response">
      <description>Standard verification response format</description>
      <format>
        <![CDATA[
## Quality Verification Response

**Status**: [PASS/FAIL]
**Feature**: [feature name]
**Verification Date**: [timestamp]

### Summary
[One-line summary of results]

### Details
[Detailed findings]

### Next Steps
[Required actions]
        ]]>
      </format>
    </pattern>
    
    <pattern name="violation_notification">
      <description>How to communicate violations</description>
      <format>
        <![CDATA[
## Quality Violation Notification

**Severity**: [CRITICAL/HIGH/MEDIUM/LOW]
**Count**: [number of violations]
**Blocking**: [yes/no]

### Critical Issues
[List of critical violations]

### Required Actions
[Specific steps to resolve]

### Timeline
[Expected resolution timeframe]
        ]]>
      </format>
    </pattern>
  </interaction_patterns>

  <approval_workflow>
    <step name="receive_request">
      <actions>
        <action>Acknowledge verification request</action>
        <action>Confirm scope and requirements</action>
        <action>Check for any special considerations</action>
      </actions>
    </step>
    
    <step name="execute_verification">
      <actions>
        <action>Run all quality checks</action>
        <action>Collect and categorize results</action>
        <action>Calculate quality score</action>
      </actions>
    </step>
    
    <step name="deliver_verdict">
      <actions>
        <action>Generate comprehensive report</action>
        <action>Provide binary pass/fail verdict</action>
        <action>Include specific remediation steps</action>
      </actions>
    </step>
    
    <step name="follow_up">
      <actions>
        <action>Track resolution of violations</action>
        <action>Re-verify after fixes</action>
        <action>Update documentation if needed</action>
      </actions>
    </step>
  </approval_workflow>

  <communication_standards>
    <standard name="clarity">
      <requirements>
        <requirement>Use clear, unambiguous language</requirement>
        <requirement>Provide specific file/line references</requirement>
        <requirement>Include exact error messages</requirement>
        <requirement>State requirements explicitly</requirement>
      </requirements>
    </standard>
    
    <standard name="actionability">
      <requirements>
        <requirement>Provide specific remediation steps</requirement>
        <requirement>Include relevant documentation links</requirement>
        <requirement>Give clear next steps</requirement>
        <requirement>Set realistic expectations</requirement>
      </requirements>
    </standard>
    
    <standard name="consistency">
      <requirements>
        <requirement>Use standardized report format</requirement>
        <requirement>Apply consistent severity levels</requirement>
        <requirement>Maintain uniform terminology</requirement>
        <requirement>Follow established patterns</requirement>
      </requirements>
    </standard>
  </communication_standards>

  <escalation_procedures>
    <procedure name="disputed_violation">
      <steps>
        <step>Reference specific documentation rule</step>
        <step>Provide clear justification</step>
        <step>Escalate to Technical Design Agent</step>
        <step>Document final resolution</step>
      </steps>
    </procedure>
    
    <procedure name="complex_violation">
      <steps>
        <step>Document the complexity</step>
        <step>Explain architectural implications</step>
        <step>Escalate to Orchestrator</step>
        <step>Update standards if needed</step>
      </steps>
    </procedure>
    
    <procedure name="emergency_exception">
      <steps>
        <step>Document critical business need</step>
        <step>Identify specific risks</step>
        <step>Get Orchestrator approval</step>
        <step>Create remediation plan</step>
        <step>Schedule follow-up verification</step>
      </steps>
    </procedure>
  </escalation_procedures>

  <reporting_templates>
    <template name="daily_quality_report">
      <format>
        <![CDATA[
## Daily Quality Report

**Date**: [YYYY-MM-DD]
**Total Verifications**: [count]
**Approvals**: [count]
**Rejections**: [count]

### Summary
- **Backend**: [pass/fail count]
- **Frontend**: [pass/fail count]
- **Integration**: [pass/fail count]

### Common Issues
[List of recurring violations]

### Recommendations
[Process improvements]
        ]]>
      </format>
    </template>
    
    <template name="weekly_quality_metrics">
      <format>
        <![CDATA[
## Weekly Quality Metrics

**Week**: [YYYY-W##]
**Total Features**: [count]
**Quality Score**: [average score]
**Trend**: [improving/stable/declining]

### Metrics
- **Type Safety**: [percentage]
- **Test Coverage**: [percentage]
- **Lint Compliance**: [percentage]
- **Security**: [percentage]

### Highlights
[Notable achievements or issues]
        ]]>
      </format>
    </template>
    
    <template name="quarterly_quality_review">
      <format>
        <![CDATA[
## Quarterly Quality Review

**Quarter**: [Q# YYYY]
**Overall Quality Score**: [score]
**Improvement**: [percentage change]

### Achievements
[List of quality improvements]

### Challenges
[List of persistent issues]

### Recommendations
[Strategic improvements]
        ]]>
      </format>
    </template>
  </reporting_templates>

  <quality_metrics>
    <metric name="verification_time">
      <description>Average time to complete verification</description>
      <target>5 minutes</target>
      <measurement>Track per verification</measurement>
    </metric>
    
    <metric name="false_positive_rate">
      <description>Rate of incorrectly flagged issues</description>
      <target>< 1%</target>
      <measurement>Track disputed violations</measurement>
    </metric>
    
    <metric name="resolution_time">
      <description>Time to resolve violations</description>
      <target>< 2 hours</target>
      <measurement>Track from report to fix</measurement>
    </metric>
    
    <metric name="quality_score">
      <description>Overall quality score</description>
      <target>100%</target>
      <measurement>Calculate per verification</measurement>
    </metric>
  </quality_metrics>
</communication_guidelines>