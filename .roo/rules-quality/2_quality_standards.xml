<quality_standards>
  <backend_standards>
    <standard name="mypy_configuration">
      <description>Python type checking requirements</description>
      <configuration>
        <strict_mode>enabled</strict_mode>
        <disallow_any_generics>true</disallow_any_generics>
        <disallow_untyped_defs>true</disallow_untyped_defs>
        <check_untyped_defs>true</check_untyped_defs>
        <no_implicit_optional>true</no_implicit_optional>
        <warn_redundant_casts>true</warn_redundant_casts>
        <warn_unused_ignores>true</warn_unused_ignores>
      </configuration>
      <commands>
        <command>cd server && uv run mypy src/ --show-error-codes</command>
        <command>cd server && uv run mypy src/ --strict</command>
      </commands>
      <acceptable_errors>
        <error>None - zero tolerance for type errors</error>
      </acceptable_errors>
    </standard>
    
    <standard name="ruff_configuration">
      <description>Python linting and formatting requirements</description>
      <configuration>
        <line_length>88</line_length>
        <target_version>py311</target_version>
        <select>["E", "F", "W", "I", "N", "UP", "ANN", "S", "B", "A", "C4", "DTZ", "T10", "EM", "EXE", "ISC", "ICN", "G", "INP", "PIE", "T20", "PYI", "PT", "Q", "RSE", "RET", "SLF", "SIM", "TID", "TCH", "ARG", "PTH", "ERA", "PD", "PGH", "PL", "TRY", "NPY", "RUF"]</select>
        <ignore>["S101", "ANN101", "ANN102"]</ignore>
      </configuration>
      <commands>
        <command>cd server && uv run ruff check .</command>
        <command>cd server && uv run ruff format . --check</command>
      </commands>
      <acceptable_warnings>
        <warning>None - zero tolerance for lint warnings</warning>
      </acceptable_warnings>
    </standard>
    
    <standard name="pytest_configuration">
      <description>Python testing requirements</description>
      <configuration>
        <min_coverage>85</min_coverage>
        <critical_coverage>100</critical_coverage>
        <test_patterns>
          <pattern>test_*.py</pattern>
          <pattern>*_test.py</pattern>
        </test_patterns>
      </configuration>
      <commands>
        <command>cd server && uv run pytest tests/ -v --tb=short</command>
        <command>cd server && uv run pytest tests/ --cov=src --cov-report=term-missing --cov-fail-under=85</command>
        <command>cd server && uv run pytest tests/ -m "not integration" --cov=src --cov-report=xml</command>
      </commands>
      <requirements>
        <requirement>100% test pass rate</requirement>
        <requirement>≥85% overall coverage</requirement>
        <requirement>100% critical logic coverage</requirement>
      </requirements>
    </standard>
    
    <standard name="security_checks">
      <description>Security validation requirements</description>
      <tools>
        <tool name="bandit">
          <command>cd server && uv run bandit -r src/ -f json -o bandit-report.json</command>
          <severity_levels>["HIGH", "MEDIUM"]</severity_levels>
        </tool>
      </tools>
      <requirements>
        <requirement>Zero HIGH severity issues</requirement>
        <requirement>Zero MEDIUM severity issues</requirement>
      </requirements>
    </standard>
  </backend_standards>

  <frontend_standards>
    <standard name="typescript_configuration">
      <description>TypeScript type checking requirements</description>
      <configuration>
        <strict>true</strict>
        <noImplicitAny>true</noImplicitAny>
        <strictNullChecks>true</strictNullChecks>
        <strictFunctionTypes>true</strictFunctionTypes>
        <strictBindCallApply>true</strictBindCallApply>
        <strictPropertyInitialization>true</strictPropertyInitialization>
        <noImplicitReturns>true</noImplicitReturns>
        <noFallthroughCasesInSwitch>true</noFallthroughCasesInSwitch>
        <noUncheckedIndexedAccess>true</noUncheckedIndexedAccess>
        <exactOptionalPropertyTypes>true</exactOptionalPropertyTypes>
      </configuration>
      <commands>
        <command>cd client && pnpm tsc --noEmit</command>
        <command>cd client && pnpm tsc --noEmit --project tsconfig.json</command>
      </commands>
      <acceptable_errors>
        <error>None - zero tolerance for TypeScript errors</error>
      </acceptable_errors>
    </standard>
    
    <standard name="eslint_configuration">
      <description>JavaScript/TypeScript linting requirements</description>
      <configuration>
        <extends>["next/core-web-vitals", "prettier"]</extends>
        <rules>
          <no_console>error</no_console>
          <no_debugger>error</no_debugger>
          <prefer_const>error</prefer_const>
          <no_var>error</no_var>
        </rules>
      </configuration>
      <commands>
        <command>cd client && pnpm next lint</command>
        <command>cd client && pnpm next lint --dir src/components</command>
      </commands>
      <acceptable_warnings>
        <warning>None - zero tolerance for ESLint warnings</warning>
      </acceptable_warnings>
    </standard>
    
    <standard name="prettier_configuration">
      <description>Code formatting requirements</description>
      <configuration>
        <semi>true</semi>
        <trailing_comma>es5</trailing_comma>
        <single_quote>true</single_quote>
        <print_width>80</print_width>
        <tab_width>2</tab_width>
        <use_tabs>false</use_tabs>
      </configuration>
      <commands>
        <command>cd client && pnpm prettier --check "src/**/*.{ts,tsx,js,jsx,json,md}"</command>
      </commands>
      <acceptable_formatting_issues>
        <issue>None - zero tolerance for formatting issues</issue>
      </acceptable_formatting_issues>
    </standard>
    
    <standard name="vitest_configuration">
      <description>Frontend testing requirements</description>
      <configuration>
        <min_coverage>85</min_coverage>
        <critical_coverage>100</critical_coverage>
        <test_patterns>
          <pattern>**/*.test.{ts,tsx}</pattern>
          <pattern>**/*.spec.{ts,tsx}</pattern>
        </test_patterns>
      </configuration>
      <commands>
        <command>cd client && pnpm vitest --run</command>
        <command>cd client && pnpm vitest --run --coverage</command>
        <command>cd client && pnpm vitest --run --reporter=verbose</command>
      </commands>
      <requirements>
        <requirement>100% test pass rate</requirement>
        <requirement>≥85% overall coverage</requirement>
        <requirement>100% critical component coverage</requirement>
      </requirements>
    </standard>
    
    <standard name="playwright_configuration">
      <description>E2E testing requirements</description>
      <configuration>
        <browsers>["chromium", "firefox", "webkit"]</browsers>
        <viewport>
          <width>1280</width>
          <height>720</height>
        </viewport>
      </configuration>
      <commands>
        <command>cd client && pnpm playwright test</command>
        <command>cd client && pnpm playwright test --reporter=html</command>
      </commands>
      <requirements>
        <requirement>100% E2E test pass rate</requirement>
        <requirement>All critical user flows tested</requirement>
      </requirements>
    </standard>
  </frontend_standards>

  <coverage_requirements>
    <requirement name="overall_coverage">
      <minimum>85%</minimum>
      <target>90%</target>
      <exclusions>
        <pattern>*/tests/*</pattern>
        <pattern>*/__tests__/*</pattern>
        <pattern>*/migrations/*</pattern>
        <pattern>*/node_modules/*</pattern>
      </exclusions>
    </requirement>
    
    <requirement name="critical_logic">
      <minimum>100%</minimum>
      <critical_areas>
        <area>Business logic in services</area>
        <area>API endpoint handlers</area>
        <area>Data validation logic</area>
        <area>Security implementations</area>
        <area>Error handling</area>
      </critical_areas>
    </requirement>
    
    <requirement name="branch_coverage">
      <minimum>80%</minimum>
      <target>85%</target>
    </requirement>
  </coverage_requirements>

  <reporting_standards>
    <standard name="violation_reporting">
      <format>
        <![CDATA[
## Quality Verification Report

**Status**: [PASS/FAIL]
**Date**: [ISO timestamp]
**Scope**: [Backend/Frontend/Both]

### Quality Checks

| Check | Status | Details |
|-------|--------|---------|
| MyPy Type Checking | [PASS/FAIL] | [Error count/Details] |
| Ruff Linting | [PASS/FAIL] | [Warning count/Details] |
| Pytest Tests | [PASS/FAIL] | [Pass/Fail count] |
| Coverage | [PASS/FAIL] | [Coverage %/Target] |
| ESLint | [PASS/FAIL] | [Error count/Details] |
| TypeScript | [PASS/FAIL] | [Error count/Details] |
| Vitest Tests | [PASS/FAIL] | [Pass/Fail count] |
| Playwright E2E | [PASS/FAIL] | [Pass/Fail count] |

### Violations

**File**: [path]
**Line**: [number]
**Rule**: [rule name]
**Message**: [error message]
**Severity**: [HIGH/MEDIUM/LOW]

### Next Steps

[Specific actions required to pass verification]
        ]]>
      </format>
    </standard>
    
    <standard name="approval_criteria">
      <requirements>
        <requirement>All quality checks must pass</requirement>
        <requirement>100% test pass rate</requirement>
        <requirement>Coverage targets met</requirement>
        <requirement>Zero security issues</requirement>
        <requirement>Zero formatting issues</requirement>
      </requirements>
    </standard>
  </reporting_standards>
</quality_standards>