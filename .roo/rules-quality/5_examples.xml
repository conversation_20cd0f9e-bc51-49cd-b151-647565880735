<examples>
  <complete_verification name="circuit_feature_verification">
    <description>Complete quality verification for Circuit CRUD feature</description>
    <context>Verifying the Circuit CRUD implementation meets all quality standards</context>
    
    <phase name="backend_verification">
      <title>Backend Quality Checks</title>
      <commands>
        <command>cd server && uv run mypy src/modules/electrical/ --show-error-codes</command>
        <command>cd server && uv run ruff check src/modules/electrical/</command>
        <command>cd server && uv run pytest tests/modules/electrical/ -v --cov=src.modules.electrical --cov-report=term-missing</command>
      </commands>
      <expected_results>
        <result tool="mypy">Success: no issues found in 15 source files</result>
        <result tool="ruff">All checks passed!</result>
        <result tool="pytest">15 passed, 0 failed, 0 skipped</result>
        <result tool="coverage">Coverage: 95% (target: 85%)</result>
      </expected_results>
    </phase>
    
    <phase name="frontend_verification">
      <title>Frontend Quality Checks</title>
      <commands>
        <command>cd client && pnpm tsc --noEmit --project tsconfig.json</command>
        <command>cd client && pnpm next lint --dir src/components/circuits</command>
        <command>cd client && pnpm vitest src/components/circuits/ --run --coverage</command>
      </commands>
      <expected_results>
        <result tool="typescript">No errors found</result>
        <result tool="eslint">✓ No ESLint warnings or errors</result>
        <result tool="vitest">8 passed, 0 failed, 0 skipped</result>
        <result tool="coverage">Coverage: 92% (target: 85%)</result>
      </expected_results>
    </phase>
    
    <phase name="integration_verification">
      <title>Integration Testing</title>
      <commands>
        <command>cd client && pnpm playwright test tests/e2e/circuit-workflow.spec.ts</command>
      </commands>
      <expected_results>
        <result tool="playwright">3 passed, 0 failed, 0 skipped</result>
      </expected_results>
    </phase>
    
    <phase name="final_report">
      <title>Quality Verification Report</title>
      <report>
        <![CDATA[
# Code Quality Verification Report

**Status**: ✅ PASS
**Timestamp**: 2024-01-15T14:30:00Z
**Scope**: Circuit CRUD Feature (Backend + Frontend)

## Summary
- **Total Checks**: 12
- **Passed**: 12
- **Failed**: 0
- **Success Rate**: 100%

## Detailed Results

### Backend Quality
- **MyPy**: ✅ PASS - 0 errors
- **Ruff**: ✅ PASS - 0 warnings
- **Pytest**: ✅ PASS - 15/15 tests
- **Coverage**: ✅ PASS - 95% (target: 85%)
- **Security**: ✅ PASS - 0 issues

### Frontend Quality
- **TypeScript**: ✅ PASS - 0 errors
- **ESLint**: ✅ PASS - 0 warnings
- **Prettier**: ✅ PASS - All files formatted
- **Vitest**: ✅ PASS - 8/8 tests
- **Playwright**: ✅ PASS - 3/3 tests

## Coverage Details
- **Backend**: 95% overall, 100% critical logic
- **Frontend**: 92% overall, 100% component logic
- **E2E**: 100% critical user flows

## Approval
**Quality Score**: 100%
**Recommendation**: APPROVE for production deployment
        ]]>
      </report>
    </phase>
  </complete_verification>

  <violation_examples>
    <example name="type_error_violation">
      <description>MyPy type error in backend code</description>
      <violation>
        <![CDATA[
### Type Error

**File**: `src/modules/electrical/services/circuit_service.py`
**Line**: 45
**Error Code**: arg-type
**Message**: Argument "voltage" to "calculate_power" has incompatible type "str"; expected "float"
**Rule**: Zero tolerance for type safety violations

**Impact**: This violates the project's zero-tolerance policy for type safety.
**Action Required**: Fix the type error by ensuring voltage parameter is float type.
        ]]>
      </violation>
    </example>
    
    <example name="test_failure_violation">
      <description>Test failure in frontend component</description>
      <violation>
        <![CDATA[
### Test Failure

**Test**: `CircuitForm › should validate required fields`
**File**: `src/components/circuits/CircuitForm.test.tsx`
**Error**: expect(received).toBeInTheDocument()
Received element is not in the document
**Stack Trace**: [full stack trace]

**Impact**: This indicates a regression in form validation.
**Action Required**: Fix the form validation logic or update the test.
        ]]>
      </violation>
    </example>
    
    <example name="coverage_gap_violation">
      <description>Insufficient test coverage</description>
      <violation>
        <![CDATA[
### Coverage Gap

**File**: `src/modules/electrical/services/circuit_service.py`
**Lines**: 78-82, 95-97
**Branches**: 2-3 (else branch not covered)
**Functions**: `validate_circuit_data` (partially covered)
**Current Coverage**: 78%
**Required Coverage**: 85%

**Impact**: Insufficient test coverage for critical business logic.
**Action Required**: Add tests to cover the missing lines and branches.
        ]]>
      </violation>
    </example>
    
    <example name="linting_violation">
      <description>ESLint warning in frontend code</description>
      <violation>
        <![CDATA[
### Linting Violation

**File**: `src/components/circuits/CircuitList.tsx`
**Line**: 23
**Rule**: @typescript-eslint/no-explicit-any
**Message**: Unexpected any. Specify a more specific type.
**Severity**: HIGH

**Impact**: This violates the project's zero-tolerance policy for type safety.
**Action Required**: Replace 'any' with specific TypeScript type.
        ]]>
      </violation>
    </example>
  </violation_examples>

  <remediation_examples>
    <example name="fixing_type_error">
      <description>How to fix a MyPy type error</description>
      <steps>
        <step>Identify the type error location</step>
        <step>Determine the expected type</step>
        <step>Add proper type annotation</step>
        <step>Verify fix with MyPy</step>
      </steps>
      <code>
        <![CDATA[
# Before (with error)
def calculate_power(voltage, current):
    return voltage * current  # MyPy: Missing type annotations

# After (fixed)
def calculate_power(voltage: float, current: float) -> float:
    return voltage * current  # MyPy: No errors
        ]]>
      </code>
    </example>
    
    <example name="fixing_test_failure">
      <description>How to fix a failing test</description>
      <steps>
        <step>Read the test failure message</step>
        <step>Identify the failing assertion</step>
        <step>Fix the implementation or test</step>
        <step>Re-run the test</step>
      </steps>
      <code>
        <![CDATA[
// Before (failing test)
expect(screen.getByText('Circuit Name')).toBeInTheDocument();
// Error: Unable to find element with text "Circuit Name"

// After (fixed)
expect(screen.getByLabelText('Circuit Name')).toBeInTheDocument();
// Test passes
        ]]>
      </code>
    </example>
    
    <example name="improving_coverage">
      <description>How to improve test coverage</description>
      <steps>
        <step>Identify uncovered code</step>
        <step>Write targeted tests</step>
        <step>Verify coverage improvement</step>
        <step>Ensure edge cases are covered</step>
      </steps>
      <code>
        <![CDATA[
# Before (missing coverage)
def validate_voltage(voltage: float) -> bool:
    return voltage > 0  # Not tested for edge cases

# After (with tests)
def validate_voltage(voltage: float) -> bool:
    """Validate voltage is positive."""
    return voltage > 0

# Test
def test_validate_voltage():
    assert validate_voltage(12.0) is True
    assert validate_voltage(0.0) is False
    assert validate_voltage(-1.0) is False
        ]]>
      </code>
    </example>
  </remediation_examples>

  <automation_examples>
    <example name="quality_check_script">
      <description>Automated quality verification script</description>
      <script>
        <![CDATA[
#!/bin/bash
# Automated Quality Verification Script

set -e

echo "🔍 Starting Quality Verification..."

# Backend checks
echo "📊 Backend Quality Checks"
cd server

echo "  Running MyPy..."
uv run mypy src/ --show-error-codes || exit 1

echo "  Running Ruff..."
uv run ruff check . || exit 1

echo "  Running Tests..."
uv run pytest tests/ --cov=src --cov-fail-under=85 || exit 1

# Frontend checks
echo "📊 Frontend Quality Checks"
cd ../client

echo "  Running TypeScript..."
pnpm tsc --noEmit || exit 1

echo "  Running ESLint..."
pnpm next lint --max-warnings=0 || exit 1

echo "  Running Tests..."
pnpm vitest --run --coverage || exit 1

echo "  Running E2E Tests..."
pnpm playwright test || exit 1

echo "✅ All quality checks passed!"
        ]]>
      </script>
    </example>
    
    <example name="ci_pipeline">
      <description>GitHub Actions quality pipeline</description>
      <workflow>
        <![CDATA[
name: Quality Verification

on:
  pull_request:
    branches: [main]

jobs:
  quality-check:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Backend Quality
        run: |
          cd server
          uv run mypy src/ --show-error-codes
          uv run ruff check .
          uv run pytest tests/ --cov=src --cov-fail-under=85
      
      - name: Frontend Quality
        run: |
          cd client
          pnpm tsc --noEmit
          pnpm next lint --max-warnings=0
          pnpm vitest --run --coverage
          pnpm playwright test
        ]]>
      </workflow>
    </example>
  </automation_examples>
</examples>