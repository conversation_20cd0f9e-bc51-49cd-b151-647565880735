<task_templates>
  <backend_task_templates>
    <template name="database_schema">
      <task_id>DB-{entity}-{number}</task_id>
      <title>Create {entity} database schema</title>
      <description>
        Create SQLAlchemy model for {entity} with fields: {fields}
        Include proper typing, relationships, and indexes
      </description>
      <estimated_time>30 minutes</estimated_time>
      <dependencies>Design approval</dependencies>
      <acceptance_criteria>
        <criterion>Model created with proper SQLAlchemy syntax</criterion>
        <criterion>All fields have correct types and constraints</criterion>
        <criterion>Relationships defined with proper foreign keys</criterion>
        <criterion>Docstrings included</criterion>
      </acceptance_criteria>
      <quality_requirements>
        <requirement>Follow SQLAlchemy best practices</requirement>
        <requirement>Include type hints</requirement>
        <requirement>Add database indexes for performance</requirement>
      </quality_requirements>
    </template>

    <template name="pydantic_schema">
      <task_id>PY-{entity}-{number}</task_id>
      <title>Create {entity} Pydantic schemas</title>
      <description>
        Create Pydantic schemas for {entity}: Create, Read, Update, ListResponse
        Include proper validation and documentation
      </description>
      <estimated_time>30 minutes</estimated_time>
      <dependencies>DB-{entity}-001</dependencies>
      <acceptance_criteria>
        <criterion>All required schemas created</criterion>
        <criterion>Validation rules implemented</criterion>
        <criterion>Documentation strings included</criterion>
      </acceptance_criteria>
      <quality_requirements>
        <requirement>Use Pydantic v2 syntax</requirement>
        <requirement>Include field validators where needed</requirement>
        <requirement>Follow project naming conventions</requirement>
      </quality_requirements>
    </template>

    <template name="repository">
      <task_id>REPO-{entity}-{number}</task_id>
      <title>Create {entity} repository</title>
      <description>
        Implement repository class for {entity} with CRUD operations
        Include proper typing and error handling
      </description>
      <estimated_time>30 minutes</estimated_time>
      <dependencies>PY-{entity}-001</dependencies>
      <acceptance_criteria>
        <criterion>Repository class created with all CRUD methods</criterion>
        <criterion>Proper typing throughout</criterion>
        <criterion>Error handling implemented</criterion>
      </acceptance_criteria>
      <quality_requirements>
        <requirement>Follow repository pattern</requirement>
        <requirement>Use SQLAlchemy best practices</requirement>
        <requirement>Include comprehensive docstrings</requirement>
      </quality_requirements>
    </template>

    <template name="service">
      <task_id>SVC-{entity}-{number}</task_id>
      <title>Create {entity} service</title>
      <description>
        Implement service layer for {entity} business logic
        Include validation and business rules
      </description>
      <estimated_time>30 minutes</estimated_time>
      <dependencies>REPO-{entity}-001</dependencies>
      <acceptance_criteria>
        <criterion>Service class created with business methods</criterion>
        <criterion>Business logic properly encapsulated</criterion>
        <criterion>Input validation implemented</criterion>
      </acceptance_criteria>
      <quality_requirements>
        <requirement>Follow service layer pattern</requirement>
        <requirement>Include comprehensive error handling</requirement>
        <requirement>Write tests first (TDD)</requirement>
      </quality_requirements>
    </template>

    <template name="api_endpoint">
      <task_id>API-{endpoint}-{number}</task_id>
      <title>Create {endpoint} API endpoint</title>
      <description>
        Implement REST endpoint for {endpoint} using FastAPI
        Include proper validation and error handling
      </description>
      <estimated_time>30 minutes</estimated_time>
      <dependencies>SVC-{entity}-001</dependencies>
      <acceptance_criteria>
        <criterion>Endpoint created with correct HTTP method</criterion>
        <criterion>Request/response validation working</criterion>
        <criterion>Error responses properly formatted</criterion>
      </acceptance_criteria>
      <quality_requirements>
        <requirement>Use FastAPI best practices</requirement>
        <requirement>Include OpenAPI documentation</requirement>
        <requirement>Write integration tests</requirement>
      </quality_requirements>
    </template>

    <template name="unit_test">
      <task_id>TEST-{component}-{number}</task_id>
      <title>Write tests for {component}</title>
      <description>
        Write comprehensive unit tests for {component} using pytest
        Include edge cases and error scenarios
      </description>
      <estimated_time>30 minutes</estimated_time>
      <dependencies>{component}-001</dependencies>
      <acceptance_criteria>
        <criterion>All public methods have tests</criterion>
        <criterion>Edge cases covered</criterion>
        <criterion>Tests pass with 100% success rate</criterion>
      </acceptance_criteria>
      <quality_requirements>
        <requirement>Follow pytest best practices</requirement>
        <requirement>Use fixtures for setup</requirement>
        <requirement>Include descriptive test names</requirement>
      </quality_requirements>
    </template>
  </backend_task_templates>

  <frontend_task_templates>
    <template name="type_definitions">
      <task_id>TYPE-{feature}-{number}</task_id>
      <title>Create TypeScript types for {feature}</title>
      <description>
        Define TypeScript interfaces and types for {feature}
        Include API response types and component props
      </description>
      <estimated_time>30 minutes</estimated_time>
      <dependencies>API-{feature}-001</dependencies>
      <acceptance_criteria>
        <criterion>All types properly defined</criterion>
        <criterion>Types match API contracts</criterion>
        <criterion>Documentation included</criterion>
      </acceptance_criteria>
      <quality_requirements>
        <requirement>Follow TypeScript best practices</requirement>
        <requirement>Use strict typing</requirement>
        <requirement>Include JSDoc comments</requirement>
      </quality_requirements>
    </template>

    <template name="api_hook">
      <task_id>HOOK-{feature}-{number}</task_id>
      <title>Create React Query hook for {feature}</title>
      <description>
        Create custom React Query hook for {feature} API integration
        Include caching and error handling
      </description>
      <estimated_time>30 minutes</estimated_time>
      <dependencies>TYPE-{feature}-001</dependencies>
      <acceptance_criteria>
        <criterion>Hook created with proper typing</criterion>
        <criterion>Loading and error states handled</criterion>
        <criterion>Cache invalidation implemented</criterion>
      </acceptance_criteria>
      <quality_requirements>
        <requirement>Follow React Query best practices</requirement>
        <requirement>Include comprehensive error handling</requirement>
        <requirement>Write hook tests</requirement>
      </quality_requirements>
    </template>

    <template name="component">
      <task_id>COMP-{component}-{number}</task_id>
      <title>Create {component} React component</title>
      <description>
        Build {component} React component with proper props and state
        Include accessibility and responsive design
      </description>
      <estimated_time>30 minutes</estimated_time>
      <dependencies>HOOK-{feature}-001</dependencies>
      <acceptance_criteria>
        <criterion>Component created with proper props</criterion>
        <criterion>Responsive design implemented</criterion>
        <criterion>Accessibility standards met</criterion>
      </acceptance_criteria>
      <quality_requirements>
        <requirement>Follow React best practices</requirement>
        <requirement>Use TypeScript strictly</requirement>
        <requirement>Write component tests</requirement>
      </quality_requirements>
    </template>

    <template name="state_management">
      <task_id>STATE-{feature}-{number}</task_id>
      <title>Create Zustand store for {feature}</title>
      <description>
        Implement Zustand store for {feature} client state management
        Include actions and selectors
      </description>
      <estimated_time>30 minutes</estimated_time>
      <dependencies>COMP-{component}-001</dependencies>
      <acceptance_criteria>
        <criterion>Store created with proper typing</criterion>
        <criterion>Actions and selectors implemented</criterion>
        <criterion>Persistency configured if needed</criterion>
      </acceptance_criteria>
      <quality_requirements>
        <requirement>Follow Zustand best practices</requirement>
        <requirement>Include middleware for dev tools</requirement>
        <requirement>Write store tests</requirement>
      </quality_requirements>
    </template>

    <template name="component_test">
      <task_id>TEST-{component}-{number}</task_id>
      <title>Write tests for {component}</title>
      <description>
        Write comprehensive component tests using React Testing Library
        Include user interaction and edge cases
      </description>
      <estimated_time>30 minutes</estimated_time>
      <dependencies>COMP-{component}-001</dependencies>
      <acceptance_criteria>
        <criterion>All component functionality tested</criterion>
        <criterion>User interactions covered</criterion>
        <criterion>Edge cases handled</criterion>
      </acceptance_criteria>
      <quality_requirements>
        <requirement>Follow React Testing Library best practices</requirement>
        <requirement>Use user-event for interactions</requirement>
        <requirement>Include accessibility tests</requirement>
      </quality_requirements>
    </template>
  </frontend_task_templates>

  <verification_task_templates>
    <template name="backend_quality">
      <task_id>QUAL-BE-{component}</task_id>
      <title>Run backend quality checks for {component}</title>
      <description>
        Execute all backend quality checks for {component}
        Include type checking, linting, and testing
      </description>
      <estimated_time>30 minutes</estimated_time>
      <dependencies>All backend tasks for {component}</dependencies>
      <acceptance_criteria>
        <criterion>All tests pass</criterion>
        <criterion>No linting errors</criterion>
        <criterion>Type checking passes</criterion>
      </acceptance_criteria>
      <quality_requirements>
        <requirement>Run full test suite</requirement>
        <requirement>Check test coverage</requirement>
        <requirement>Verify security scan passes</requirement>
      </quality_requirements>
    </template>

    <template name="frontend_quality">
      <task_id>QUAL-FE-{component}</task_id>
      <title>Run frontend quality checks for {component}</title>
      <description>
        Execute all frontend quality checks for {component}
        Include type checking, linting, and testing
      </description>
      <estimated_time>30 minutes</estimated_time>
      <dependencies>All frontend tasks for {component}</dependencies>
      <acceptance_criteria>
        <criterion>All tests pass</criterion>
        <criterion>No linting errors</criterion>
        <criterion>Type checking passes</criterion>
      </acceptance_criteria>
      <quality_requirements>
        <requirement>Run full test suite</requirement>
        <requirement>Check test coverage</requirement>
        <requirement>Verify build succeeds</requirement>
      </quality_requirements>
    </template>
  </verification_task_templates>

  <documentation_task_templates>
    <template name="api_docs">
      <task_id>DOC-API-{feature}</task_id>
      <title>Update API documentation for {feature}</title>
      <description>
        Update API documentation with new endpoints
        Include request/response examples
      </description>
      <estimated_time>30 minutes</estimated_time>
      <dependencies>All API endpoints completed</dependencies>
      <acceptance_criteria>
        <criterion>All endpoints documented</criterion>
        <criterion>Examples provided</criterion>
        <criterion>Documentation is accurate</criterion>
      </acceptance_criteria>
      <quality_requirements>
        <requirement>Follow OpenAPI specification</requirement>
        <requirement>Include error response examples</requirement>
        <requirement>Update README if needed</requirement>
      </quality_requirements>
    </template>

    <template name="component_docs">
      <task_id>DOC-COMP-{component}</task_id>
      <title>Document {component} React component</title>
      <description>
        Create comprehensive documentation for {component}
        Include props, usage examples, and guidelines
      </description>
      <estimated_time>30 minutes</estimated_time>
      <dependencies>Component completed and tested</dependencies>
      <acceptance_criteria>
        <criterion>Props documented</criterion>
        <criterion>Usage examples provided</criterion>
        <criterion>Storybook stories created</criterion>
      </acceptance_criteria>
      <quality_requirements>
        <requirement>Follow component documentation standards</requirement>
        <requirement>Include interactive examples</requirement>
        <requirement>Update component library docs</requirement>
      </quality_requirements>
    </template>
  </documentation_task_templates>
</task_templates>