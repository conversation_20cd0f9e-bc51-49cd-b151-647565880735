<planning_examples>
  <example name="jwt_authentication_plan">
    <description>Complete task plan for JWT authentication system</description>
    <design_input>
      <feature>JWT authentication with refresh tokens</feature>
      <components>
        <component>User authentication endpoints</component>
        <component>Token generation service</component>
        <component>Refresh token mechanism</component>
        <component>Frontend auth context</component>
      </components>
    </design_input>
    
    <task_plan>
      <phase name="implementation">
        <backend_tasks>
          <task id="BE-001" title="Create refresh_tokens table schema" estimated_time="30m">
            <description>Create SQLAlchemy model for refresh_tokens with user_id, token, expires_at fields</description>
            <dependencies>Design approval</dependencies>
            <acceptance_criteria>Model created with proper relationships</acceptance_criteria>
          </task>
          
          <task id="BE-002" title="Create User Pydantic schemas" estimated_time="30m">
            <description>Create UserLoginRequest, TokenResponse, TokenRefreshRequest schemas</description>
            <dependencies>BE-001</dependencies>
            <acceptance_criteria>All schemas with proper validation</acceptance_criteria>
          </task>
          
          <task id="BE-003" title="Create RefreshToken repository" estimated_time="30m">
            <description>Implement repository for refresh token CRUD operations</description>
            <dependencies>BE-002</dependencies>
            <acceptance_criteria>Repository with all CRUD methods</acceptance_criteria>
          </task>
          
          <task id="BE-004" title="Create AuthenticationService" estimated_time="30m">
            <description>Implement service for user authentication and token generation</description>
            <dependencies>BE-003</dependencies>
            <acceptance_criteria>Service with authenticate, generate_tokens methods</acceptance_criteria>
          </task>
          
          <task id="BE-005" title="Create login endpoint" estimated_time="30m">
            <description>Implement POST /api/v1/auth/login endpoint</description>
            <dependencies>BE-004</dependencies>
            <acceptance_criteria>Endpoint returns tokens on valid credentials</acceptance_criteria>
          </task>
          
          <task id="BE-006" title="Create token refresh endpoint" estimated_time="30m">
            <description>Implement POST /api/v1/auth/refresh endpoint</description>
            <dependencies>BE-005</dependencies>
            <acceptance_criteria>Endpoint returns new access token</acceptance_criteria>
          </task>
          
          <task id="BE-007" title="Create logout endpoint" estimated_time="30m">
            <description>Implement POST /api/v1/auth/logout endpoint</description>
            <dependencies>BE-006</dependencies>
            <acceptance_criteria>Endpoint invalidates refresh token</acceptance_criteria>
          </task>
          
          <task id="BE-008" title="Write AuthenticationService tests" estimated_time="30m">
            <description>Write unit tests for AuthenticationService using pytest</description>
            <dependencies>BE-004</dependencies>
            <acceptance_criteria>All service methods tested</acceptance_criteria>
          </task>
          
          <task id="BE-009" title="Write auth endpoint tests" estimated_time="30m">
            <description>Write integration tests for auth endpoints</description>
            <dependencies>BE-007</dependencies>
            <acceptance_criteria>All endpoints tested with various scenarios</acceptance_criteria>
          </task>
        </backend_tasks>
        
        <frontend_tasks>
          <task id="FE-001" title="Create auth TypeScript types" estimated_time="30m">
            <description>Define User, AuthState, TokenResponse types</description>
            <dependencies>BE-002</dependencies>
            <acceptance_criteria>Types match API contracts</acceptance_criteria>
          </task>
          
          <task id="FE-002" title="Create auth API hooks" estimated_time="30m">
            <description>Create React Query hooks for login, refresh, logout</description>
            <dependencies>FE-001</dependencies>
            <acceptance_criteria>Hooks with proper typing and error handling</acceptance_criteria>
          </task>
          
          <task id="FE-003" title="Create AuthProvider component" estimated_time="30m">
            <description>Implement authentication context provider</description>
            <dependencies>FE-002</dependencies>
            <acceptance_criteria>Provider with auth state management</acceptance_criteria>
          </task>
          
          <task id="FE-004" title="Create LoginForm component" estimated_time="30m">
            <description>Build login form with validation</description>
            <dependencies>FE-003</dependencies>
            <acceptance_criteria>Form with email/password validation</acceptance_criteria>
          </task>
          
          <task id="FE-005" title="Create auth component tests" estimated_time="30m">
            <description>Write tests for auth components using React Testing Library</description>
            <dependencies>FE-004</dependencies>
            <acceptance_criteria>All components tested</acceptance_criteria>
          </task>
        </frontend_tasks>
      </phase>
      
      <phase name="verification">
        <tasks>
          <task id="QUAL-001" title="Run backend quality checks" estimated_time="30m">
            <description>Execute mypy, ruff, pytest, and bandit for backend</description>
            <dependencies>BE-009</dependencies>
            <acceptance_criteria>All checks pass without errors</acceptance_criteria>
          </task>
          
          <task id="QUAL-002" title="Run frontend quality checks" estimated_time="30m">
            <description>Execute TypeScript, ESLint, Vitest, and build for frontend</description>
            <dependencies>FE-005</dependencies>
            <acceptance_criteria>All checks pass without errors</acceptance_criteria>
          </task>
        </tasks>
      </phase>
      
      <phase name="documentation">
        <tasks>
          <task id="DOC-001" title="Update API documentation" estimated_time="30m">
            <description>Document auth endpoints in OpenAPI format</description>
            <dependencies>QUAL-001</dependencies>
            <acceptance_criteria>Complete API documentation</acceptance_criteria>
          </task>
          
          <task id="DOC-002" title="Update component documentation" estimated_time="30m">
            <description>Document auth components and usage</description>
            <dependencies>QUAL-002</dependencies>
            <acceptance_criteria>Complete component documentation</acceptance_criteria>
          </task>
        </tasks>
      </phase>
    </task_plan>
  </example>
  
  <example name="project_crud_plan">
    <description>Task plan for project management CRUD operations</description>
    <design_input>
      <feature>Project CRUD operations</feature>
      <approach>CRUD Endpoint Factory pattern</approach>
    </design_input>
    
    <task_plan>
      <phase name="implementation">
        <backend_tasks>
          <task id="BE-CRUD-001" title="Create Project database model" estimated_time="30m">
            <description>Create SQLAlchemy Project model with name, description, client_name, status fields</description>
            <dependencies>Design approval</dependencies>
          </task>
          
          <task id="BE-CRUD-002" title="Create Project Pydantic schemas" estimated_time="30m">
            <description>Create ProjectCreate, ProjectRead, ProjectUpdate, ProjectListResponse schemas</description>
            <dependencies>BE-CRUD-001</dependencies>
          </task>
          
          <task id="BE-CRUD-003" title="Create ProjectService" estimated_time="30m">
            <description>Implement ProjectService extending BaseService</description>
            <dependencies>BE-CRUD-002</dependencies>
          </task>
          
          <task id="BE-CRUD-004" title="Create Project CRUD router" estimated_time="30m">
            <description>Implement CRUD router using create_simple_crud_router</description>
            <dependencies>BE-CRUD-003</dependencies>
          </task>
          
          <task id="BE-CRUD-005" title="Write Project service tests" estimated_time="30m">
            <description>Write unit tests for ProjectService</description>
            <dependencies>BE-CRUD-003</dependencies>
          </task>
          
          <task id="BE-CRUD-006" title="Write Project API tests" estimated_time="30m">
            <description>Write integration tests for Project CRUD endpoints</description>
            <dependencies>BE-CRUD-004</dependencies>
          </task>
        </backend_tasks>
        
        <frontend_tasks>
          <task id="FE-CRUD-001" title="Create Project types" estimated_time="30m">
            <description>Define TypeScript types for Project API</description>
            <dependencies>BE-CRUD-002</dependencies>
          </task>
          
          <task id="FE-CRUD-002" title="Create Project API hooks" estimated_time="30m">
            <description>Create React Query hooks for Project CRUD operations</description>
            <dependencies>FE-CRUD-001</dependencies>
          </task>
          
          <task id="FE-CRUD-003" title="Create ProjectList component" estimated_time="30m">
            <description>Build component for listing projects with search and pagination</description>
            <dependencies>FE-CRUD-002</dependencies>
          </task>
          
          <task id="FE-CRUD-004" title="Create ProjectForm component" estimated_time="30m">
            <description>Build form component for creating/editing projects</description>
            <dependencies>FE-CRUD-003</dependencies>
          </task>
          
          <task id="FE-CRUD-005" title="Write Project component tests" estimated_time="30m">
            <description>Write tests for Project components</description>
            <dependencies>FE-CRUD-004</dependencies>
          </task>
        </frontend_tasks>
      </phase>
    </task_plan>
  </example>
  
  <example name="complex_calculation_service_plan">
    <description>Task plan for electrical load calculation service</description>
    <design_input>
      <feature>NEC electrical load calculations</feature>
      <complexity>High - requires business logic</complexity>
    </design_input>
    
    <task_plan>
      <phase name="implementation">
        <backend_tasks>
          <task id="BE-CALC-001" title="Create LoadCalculation database model" estimated_time="30m">
            <description>Create SQLAlchemy model for electrical load calculations</description>
            <dependencies>Design approval</dependencies>
          </task>
          
          <task id="BE-CALC-002" title="Create NEC calculation service" estimated_time="30m">
            <description>Implement NECLoadCalculationService with calculation methods</description>
            <dependencies>BE-CALC-001</dependencies>
          </task>
          
          <task id="BE-CALC-003" title="Create calculation endpoints" estimated_time="30m">
            <description>Implement POST /api/v1/calculate/load endpoint</description>
            <dependencies>BE-CALC-002</dependencies>
          </task>
          
          <task id="BE-CALC-004" title="Write calculation service tests" estimated_time="30m">
            <description>Write comprehensive tests for calculation logic</description>
            <dependencies>BE-CALC-002</dependencies>
          </task>
        </backend_tasks>
        
        <frontend_tasks>
          <task id="FE-CALC-001" title="Create calculation form component" estimated_time="30m">
            <description>Build form for electrical load calculation inputs</description>
            <dependencies>BE-CALC-003</dependencies>
          </task>
          
          <task id="FE-CALC-002" title="Create results display component" estimated_time="30m">
            <description>Build component to display calculation results</description>
            <dependencies>FE-CALC-001</dependencies>
          </task>
        </frontend_tasks>
      </phase>
    </task_plan>
  </example>
</planning_examples>