<best_practices>
  <general_principles>
    <principle priority="high">
      <name>Test-Driven Development (TDD)</name>
      <description>Always write tests before implementation code</description>
      <rationale>Ensures code is testable, meets requirements, and prevents regressions</rationale>
      <workflow>
        <step>Write failing test (red)</step>
        <step>Write minimal code to pass (green)</step>
        <step>Refactor for quality (refactor)</step>
      </workflow>
    </principle>
    
    <principle priority="high">
      <name>Zero Tolerance Policies</name>
      <description>Strict adherence to all quality standards without exceptions</description>
      <rationale>Maintains professional-grade code quality and prevents technical debt</rationale>
      <standards>
        <standard>100% test pass rate</standard>
        <standard>MyPy type checking with no errors</standard>
        <standard>Ruff/ESLint with no warnings</standard>
        <standard>Comprehensive test coverage</standard>
      </standards>
    </principle>
    
    <principle priority="high">
      <name>5-Layer Architecture</name>
      <description>Follow the established backend architecture pattern</description>
      <layers>
        <layer>API Layer - HTTP endpoints and request/response handling</layer>
        <layer>Service Layer - Business logic and orchestration</layer>
        <layer>Repository Layer - Data access and persistence</layer>
        <layer>Model Layer - Database models and schemas</layer>
        <layer>Integration Layer - External services and utilities</layer>
      </layers>
    </principle>
  </general_principles>

  <backend_conventions>
    <convention category="naming">
      <rule>Use snake_case for Python variables and functions</rule>
      <rule>Use PascalCase for class names</rule>
      <rule>Use UPPER_SNAKE_CASE for constants</rule>
      <examples>
        <good>user_service.py, calculate_total_power(), MAX_VOLTAGE</good>
        <bad>userService.py, calculateTotalPower(), maxVoltage</bad>
      </examples>
    </convention>
    
    <convention category="structure">
      <rule>Organize code by feature/domain, not by type</rule>
      <template>
        src/
        ├── modules/
        │   └── electrical/
        │       ├── models/
        │       ├── repositories/
        │       ├── services/
        │       └── api/
      </template>
    </convention>
    
    <convention category="api_design">
      <rule>Use RESTful conventions for API endpoints</rule>
      <rule>Implement consistent error response format</rule>
      <rule>Use proper HTTP status codes</rule>
      <examples>
        <good>GET /api/v1/circuits/{id}, 201 Created, 404 Not Found</good>
        <bad>GET /getCircuit, 200 OK for errors</bad>
      </examples>
    </convention>
  </backend_conventions>

  <frontend_conventions>
    <convention category="naming">
      <rule>Use camelCase for variables and functions</rule>
      <rule>Use PascalCase for React components</rule>
      <rule>Use kebab-case for CSS classes</rule>
      <examples>
        <good>useUserData(), UserProfile.tsx, bg-primary-500</good>
        <bad>use_user_data(), user_profile.tsx, bgPrimary500</bad>
      </examples>
    </convention>
    
    <convention category="component_structure">
      <rule>Follow atomic design principles</rule>
      <hierarchy>
        <level>Atoms - Basic UI elements</level>
        <level>Molecules - Simple component groups</level>
        <level>Organisms - Complex component compositions</level>
        <level>Templates - Page layouts</level>
        <level>Pages - Complete views</level>
      </hierarchy>
    </convention>
    
    <convention category="state_management">
      <rule>Use Zustand for global state</rule>
      <rule>Use React Query for server state</rule>
      <rule>Use local state for component-specific data</rule>
    </convention>
  </frontend_conventions>

  <testing_standards>
    <standard category="backend">
      <requirement>Write unit tests for all service methods</requirement>
      <requirement>Write integration tests for all API endpoints</requirement>
      <requirement>Use pytest fixtures for test data</requirement>
      <requirement>Mock external dependencies</requirement>
      <structure>
        <file>test_service.py - Service layer tests</file>
        <file>test_api.py - API endpoint tests</file>
        <file>test_repository.py - Repository tests</file>
      </structure>
    </standard>
    
    <standard category="frontend">
      <requirement>Write unit tests for all React components</requirement>
      <requirement>Write integration tests for user workflows</requirement>
      <requirement>Use React Testing Library for component tests</requirement>
      <requirement>Write e2e tests with Playwright</requirement>
      <structure>
        <file>Component.test.tsx - Component unit tests</file>
        <file>useHook.test.ts - Hook tests</file>
        <file>workflow.spec.ts - E2E tests</file>
      </structure>
    </standard>
  </testing_standards>

  <common_patterns>
    <pattern name="crud_endpoint_factory">
      <description>Use the CRUD endpoint factory for new entities</description>
      <implementation>
        <![CDATA[
from src.core.utils.crud_endpoint_factory import create_simple_crud_router

router = create_simple_crud_router(
    entity_name="circuit",
    entity_name_plural="circuits",
    create_schema=CircuitCreate,
    read_schema=CircuitRead,
    update_schema=CircuitUpdate,
    list_response_schema=CircuitListResponse,
    service_class=CircuitService,
    service_dependency=get_circuit_service,
    id_type=int,
    searchable_fields=["name", "description"],
    sortable_fields=["name", "created_at", "updated_at"],
)
        ]]>
      </implementation>
    </pattern>
    
    <pattern name="react_query_hooks">
      <description>Use consistent React Query patterns</description>
      <implementation>
        <![CDATA[
// Hook for API calls
export function useCircuits() {
  return useQuery({
    queryKey: ['circuits'],
    queryFn: fetchCircuits,
  });
}

// Mutation hook
export function useCreateCircuit() {
  return useMutation({
    mutationFn: createCircuit,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['circuits'] });
    },
  });
}
        ]]>
      </implementation>
    </pattern>
  </common_patterns>

  <error_handling>
    <pattern name="unified_error_response">
      <description>Consistent error response format across backend</description>
      <structure>
        <![CDATA[
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field": "voltage",
      "value": "abc",
      "constraint": "must be a number"
    }
  }
}
        ]]>
      </structure>
    </pattern>
    
    <pattern name="frontend_error_handling">
      <description>Handle errors gracefully in React components</description>
      <implementation>
        <![CDATA[
const { data, error, isLoading } = useCircuits();

if (isLoading) return <LoadingSpinner />;
if (error) return <ErrorMessage error={error} />;
        ]]>
      </implementation>
    </pattern>
  </error_handling>

  <quality_checklist>
    <category name="before_implementation">
      <item>Understand all requirements from task plan</item>
      <item>Identify existing patterns to follow</item>
      <item>Set up test environment</item>
    </category>
    
    <category name="during_implementation">
      <item>Write tests first (TDD)</item>
      <item>Follow naming conventions</item>
      <item>Use established patterns</item>
      <item>Add comprehensive documentation</item>
    </category>
    
    <category name="after_implementation">
      <item>All tests pass</item>
      <item>Code quality checks pass</item>
      <item>Documentation is complete</item>
      <item>Performance is acceptable</item>
      <item>Security review completed</item>
    </category>
  </quality_checklist>
</best_practices>