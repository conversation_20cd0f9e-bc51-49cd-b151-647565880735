<examples>
  <complete_workflow name="circuit_crud_implementation">
    <description>Complete implementation of CRUD operations for Circuit entity</description>
    <context>Adding a new electrical circuit entity to the system</context>
    
    <phase name="tests_first">
      <title>Write Tests (TDD)</title>
      <files>
        <file path="server/tests/test_circuit_service.py">
          <![CDATA[
import pytest
from unittest.mock import Mock
from src.modules.electrical.services.circuit_service import CircuitService
from src.modules.electrical.schemas import CircuitCreate, CircuitRead

class TestCircuitService:
    @pytest.fixture
    def service(self, mock_repository):
        return CircuitService(mock_repository)
    
    @pytest.fixture
    def mock_repository(self):
        return Mock()
    
    async def test_create_circuit_success(self, service, mock_repository):
        # Arrange
        data = CircuitCreate(name="Test Circuit", voltage=12.0, current=5.0)
        expected = CircuitRead(id=1, name="Test Circuit", voltage=12.0, current=5.0)
        mock_repository.create.return_value = expected
        
        # Act
        result = await service.create_circuit(data)
        
        # Assert
        assert result == expected
        mock_repository.create.assert_called_once_with(data)
    
    async def test_get_circuit_not_found(self, service, mock_repository):
        mock_repository.get_by_id.return_value = None
        
        with pytest.raises(NotFoundError):
            await service.get_circuit(999)
        ]]>
        </file>
        
        <file path="client/src/components/circuits/CircuitForm.test.tsx">
          <![CDATA[
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { CircuitForm } from './CircuitForm';

describe('CircuitForm', () => {
  it('should render form fields', () => {
    render(<CircuitForm onSubmit={vi.fn()} />);
    
    expect(screen.getByLabelText(/circuit name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/voltage/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/current/i)).toBeInTheDocument();
  });

  it('should validate required fields', async () => {
    const user = userEvent.setup();
    render(<CircuitForm onSubmit={vi.fn()} />);
    
    const submitButton = screen.getByRole('button', { name: /save/i });
    await user.click(submitButton);
    
    expect(await screen.findByText(/name is required/i)).toBeInTheDocument();
  });
});
        ]]>
        </file>
      </files>
    </phase>
    
    <phase name="backend_implementation">
      <title>Backend Implementation</title>
      <files>
        <file path="server/src/modules/electrical/models/circuit.py">
          <![CDATA[
from sqlalchemy import Column, Integer, String, Float, DateTime
from sqlalchemy.sql import func
from src.core.database import Base

class Circuit(Base):
    __tablename__ = "circuits"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    voltage = Column(Float, nullable=False)
    current = Column(Float, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
        ]]>
        </file>
        
        <file path="server/src/modules/electrical/schemas/circuit.py">
          <![CDATA[
from pydantic import BaseModel, Field
from datetime import datetime

class CircuitBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    voltage: float = Field(..., gt=0, description="Voltage in volts")
    current: float = Field(..., gt=0, description="Current in amperes")

class CircuitCreate(CircuitBase):
    pass

class CircuitUpdate(CircuitBase):
    pass

class CircuitRead(CircuitBase):
    id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class CircuitListResponse(BaseModel):
    items: list[CircuitRead]
    total: int
    page: int
    size: int
        ]]>
        </file>
        
        <file path="server/src/modules/electrical/repositories/circuit_repository.py">
          <![CDATA[
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import Optional, List
from src.modules.electrical.models.circuit import Circuit

class CircuitRepository:
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def create(self, circuit_data) -> Circuit:
        circuit = Circuit(**circuit_data)
        self.session.add(circuit)
        await self.session.commit()
        await self.session.refresh(circuit)
        return circuit
    
    async def get_by_id(self, id: int) -> Optional[Circuit]:
        result = await self.session.execute(
            select(Circuit).where(Circuit.id == id)
        )
        return result.scalar_one_or_none()
    
    async def get_all(self, skip: int = 0, limit: int = 100) -> List[Circuit]:
        result = await self.session.execute(
            select(Circuit).offset(skip).limit(limit)
        )
        return result.scalars().all()
        ]]>
        </file>
        
        <file path="server/src/modules/electrical/services/circuit_service.py">
          <![CDATA[
from typing import List
from src.modules.electrical.repositories.circuit_repository import CircuitRepository
from src.modules.electrical.schemas import CircuitCreate, CircuitRead, CircuitUpdate
from src.core.exceptions import NotFoundError

class CircuitService:
    def __init__(self, repository: CircuitRepository):
        self.repository = repository
    
    async def create_circuit(self, data: CircuitCreate) -> CircuitRead:
        circuit = await self.repository.create(data.model_dump())
        return CircuitRead.model_validate(circuit)
    
    async def get_circuit(self, id: int) -> CircuitRead:
        circuit = await self.repository.get_by_id(id)
        if not circuit:
            raise NotFoundError(f"Circuit {id} not found")
        return CircuitRead.model_validate(circuit)
    
    async def list_circuits(self, skip: int = 0, limit: int = 100) -> List[CircuitRead]:
        circuits = await self.repository.get_all(skip, limit)
        return [CircuitRead.model_validate(c) for c in circuits]
        ]]>
        </file>
        
        <file path="server/src/modules/electrical/api/circuit_router.py">
          <![CDATA[
from fastapi import APIRouter, Depends
from src.core.utils.crud_endpoint_factory import create_simple_crud_router
from src.modules.electrical.schemas import (
    CircuitCreate, CircuitRead, CircuitUpdate, CircuitListResponse
)
from src.modules.electrical.services.circuit_service import CircuitService
from src.modules.electrical.dependencies import get_circuit_service

router = APIRouter(prefix="/circuits", tags=["circuits"])

crud_router = create_simple_crud_router(
    entity_name="circuit",
    entity_name_plural="circuits",
    create_schema=CircuitCreate,
    read_schema=CircuitRead,
    update_schema=CircuitUpdate,
    list_response_schema=CircuitListResponse,
    service_class=CircuitService,
    service_dependency=get_circuit_service,
    id_type=int,
    searchable_fields=["name", "description"],
    sortable_fields=["name", "created_at", "updated_at"],
)

router.include_router(crud_router)
        ]]>
        </file>
      </files>
    </phase>
    
    <phase name="frontend_implementation">
      <title>Frontend Implementation</title>
      <files>
        <file path="client/src/hooks/api/use-circuits.ts">
          <![CDATA[
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '@/lib/api-client';
import type { CircuitCreate, CircuitRead, CircuitUpdate } from '@/types/api';

export function useCircuits() {
  return useQuery({
    queryKey: ['circuits'],
    queryFn: () => apiClient.circuits.getAll(),
  });
}

export function useCircuit(id: number) {
  return useQuery({
    queryKey: ['circuits', id],
    queryFn: () => apiClient.circuits.getById(id),
    enabled: !!id,
  });
}

export function useCreateCircuit() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: CircuitCreate) => apiClient.circuits.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['circuits'] });
    },
  });
}

export function useUpdateCircuit() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: CircuitUpdate }) =>
      apiClient.circuits.update(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['circuits'] });
      queryClient.invalidateQueries({ queryKey: ['circuits', id] });
    },
  });
}

export function useDeleteCircuit() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: number) => apiClient.circuits.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['circuits'] });
    },
  });
}
        ]]>
        </file>
        
        <file path="client/src/components/circuits/CircuitForm.tsx">
          <![CDATA[
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useCreateCircuit, useUpdateCircuit } from '@/hooks/api/use-circuits';
import type { CircuitCreate, CircuitRead } from '@/types/api';

const circuitSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100),
  voltage: z.number().positive('Voltage must be positive'),
  current: z.number().positive('Current must be positive'),
});

type CircuitFormData = z.infer<typeof circuitSchema>;

interface CircuitFormProps {
  circuit?: CircuitRead;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function CircuitForm({ circuit, onSuccess, onCancel }: CircuitFormProps) {
  const createMutation = useCreateCircuit();
  const updateMutation = useUpdateCircuit();
  
  const form = useForm<CircuitFormData>({
    resolver: zodResolver(circuitSchema),
    defaultValues: circuit || {
      name: '',
      voltage: 0,
      current: 0,
    },
  });

  const onSubmit = async (data: CircuitFormData) => {
    try {
      if (circuit) {
        await updateMutation.mutateAsync({ id: circuit.id, data });
      } else {
        await createMutation.mutateAsync(data);
      }
      onSuccess?.();
    } catch (error) {
      console.error('Failed to save circuit:', error);
    }
  };

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
      <div>
        <Label htmlFor="name">Circuit Name</Label>
        <Input
          id="name"
          {...form.register('name')}
          error={form.formState.errors.name?.message}
        />
      </div>
      
      <div>
        <Label htmlFor="voltage">Voltage (V)</Label>
        <Input
          id="voltage"
          type="number"
          step="0.1"
          {...form.register('voltage', { valueAsNumber: true })}
          error={form.formState.errors.voltage?.message}
        />
      </div>
      
      <div>
        <Label htmlFor="current">Current (A)</Label>
        <Input
          id="current"
          type="number"
          step="0.1"
          {...form.register('current', { valueAsNumber: true })}
          error={form.formState.errors.current?.message}
        />
      </div>
      
      <div className="flex gap-2">
        <Button type="submit" disabled={createMutation.isPending || updateMutation.isPending}>
          {circuit ? 'Update' : 'Create'} Circuit
        </Button>
        {onCancel && (
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
        )}
      </div>
    </form>
  );
}
        ]]>
        </file>
        
        <file path="client/src/components/circuits/CircuitList.tsx">
          <![CDATA[
import { useState } from 'react';
import { useCircuits, useDeleteCircuit } from '@/hooks/api/use-circuits';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table';
import { CircuitForm } from './CircuitForm';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import type { CircuitRead } from '@/types/api';

const columns = [
  {
    accessorKey: 'name',
    header: 'Name',
  },
  {
    accessorKey: 'voltage',
    header: 'Voltage (V)',
    cell: ({ row }) => `${row.original.voltage}V`,
  },
  {
    accessorKey: 'current',
    header: 'Current (A)',
    cell: ({ row }) => `${row.original.current}A`,
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => <CircuitActions circuit={row.original} />,
  },
];

function CircuitActions({ circuit }: { circuit: CircuitRead }) {
  const deleteMutation = useDeleteCircuit();
  
  return (
    <div className="flex gap-2">
      <Button
        variant="ghost"
        size="sm"
        onClick={() => {/* TODO: Implement edit */}}
      >
        Edit
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => deleteMutation.mutate(circuit.id)}
        disabled={deleteMutation.isPending}
      >
        Delete
      </Button>
    </div>
  );
}

export function CircuitList() {
  const { data: circuits, isLoading } = useCircuits();
  const [isCreateOpen, setIsCreateOpen] = useState(false);
  
  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-bold">Circuits</h2>
        <Button onClick={() => setIsCreateOpen(true)}>
          Add Circuit
        </Button>
      </div>
      
      <DataTable
        columns={columns}
        data={circuits || []}
        loading={isLoading}
      />
      
      <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Circuit</DialogTitle>
          </DialogHeader>
          <CircuitForm
            onSuccess={() => setIsCreateOpen(false)}
            onCancel={() => setIsCreateOpen(false)}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}
        ]]>
        </file>
      </files>
    </phase>
    
    <phase name="documentation">
      <title>Documentation & Handover</title>
      <files>
        <file path="docs/api/circuits.md">
          <![CDATA[
# Circuits API

## Overview
The Circuits API provides CRUD operations for managing electrical circuits.

## Endpoints

### GET /api/v1/circuits
Retrieve a list of circuits with pagination.

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `size` (optional): Items per page (default: 20)
- `search` (optional): Search in name and description
- `sort` (optional): Sort field (name, created_at, updated_at)

**Response:**
```json
{
  "items": [...],
  "total": 100,
  "page": 1,
  "size": 20
}
```

### POST /api/v1/circuits
Create a new circuit.

**Request Body:**
```json
{
  "name": "Main Circuit",
  "voltage": 12.5,
  "current": 8.2
}
```

**Response:**
```json
{
  "id": 1,
  "name": "Main Circuit",
  "voltage": 12.5,
  "current": 8.2,
  "created_at": "2024-01-01T12:00:00Z",
  "updated_at": "2024-01-01T12:00:00Z"
}
```

### GET /api/v1/circuits/{id}
Retrieve a specific circuit by ID.

### PUT /api/v1/circuits/{id}
Update an existing circuit.

### DELETE /api/v1/circuits/{id}
Delete a circuit.
        ]]>
        </file>
        
        <file path="client/src/components/circuits/README.md">
          <![CDATA[
# Circuit Components

## Overview
React components for managing electrical circuits in the frontend.

## Components

### CircuitForm
A form component for creating and editing circuits.

**Props:**
- `circuit?: CircuitRead` - Optional circuit data for editing
- `onSuccess?: () => void` - Callback on successful save
- `onCancel?: () => void` - Callback on cancel

### CircuitList
A table component displaying all circuits with actions.

### CircuitActions
Action buttons for individual circuits (edit, delete).

## Usage Example

```tsx
import { CircuitList } from '@/components/circuits/CircuitList';

function CircuitsPage() {
  return (
    <div>
      <h1>Circuits</h1>
      <CircuitList />
    </div>
  );
}
```

## Hooks

### useCircuits
Fetches all circuits.

### useCircuit(id)
Fetches a specific circuit by ID.

### useCreateCircuit
Creates a new circuit with automatic cache invalidation.

### useUpdateCircuit
Updates an existing circuit.

### useDeleteCircuit
Deletes a circuit with confirmation.
        ]]>
        </file>
      </files>
    </phase>
  </complete_workflow>

  <testing_examples>
    <example name="integration_test">
      <description>Complete integration test for circuit API</description>
      <file path="server/tests/integration/test_circuit_api.py">
        <![CDATA[
import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

@pytest.mark.asyncio
async def test_create_and_get_circuit(
    client: AsyncClient,
    db_session: AsyncSession
):
    # Create circuit
    create_data = {
        "name": "Test Circuit",
        "voltage": 12.5,
        "current": 8.2
    }
    
    response = await client.post("/api/v1/circuits", json=create_data)
    assert response.status_code == 201
    
    created = response.json()
    assert created["name"] == "Test Circuit"
    assert created["voltage"] == 12.5
    assert created["current"] == 8.2
    
    # Get circuit
    response = await client.get(f"/api/v1/circuits/{created['id']}")
    assert response.status_code == 200
    
    retrieved = response.json()
    assert retrieved == created
        ]]>
      </file>
    </example>
    
    <example name="e2e_test">
      <description>End-to-end test for circuit workflow</description>
      <file path="client/tests/e2e/circuit-workflow.spec.ts">
        <![CDATA[
import { test, expect } from '@playwright/test';

test.describe('Circuit Management', () => {
  test('should create and view a circuit', async ({ page }) => {
    await page.goto('/circuits');
    
    // Click create button
    await page.getByRole('button', { name: 'Add Circuit' }).click();
    
    // Fill form
    await page.getByLabel('Circuit Name').fill('Test Circuit');
    await page.getByLabel('Voltage (V)').fill('12.5');
    await page.getByLabel('Current (A)').fill('8.2');
    
    // Submit form
    await page.getByRole('button', { name: 'Create Circuit' }).click();
    
    // Verify circuit appears in list
    await expect(page.getByText('Test Circuit')).toBeVisible();
    await expect(page.getByText('12.5V')).toBeVisible();
    await expect(page.getByText('8.2A')).toBeVisible();
  });
});
        ]]>
      </file>
    </example>
  </testing_examples>
</examples>