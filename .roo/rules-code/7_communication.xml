<communication_guidelines>
  <interaction_patterns>
    <pattern name="task_clarification">
      <description>How to request clarification from Task Planner Agent</description>
      <when_to_use>
        <condition>Task is ambiguous or incomplete</condition>
        <condition>Requirements are unclear</condition>
        <condition>Dependencies are missing</condition>
      </when_to_use>
      <format>
        <![CDATA[
I need clarification on the following task:

**Task**: [specific task from plan]
**Issue**: [what's unclear]
**Options**: [possible interpretations]
**Recommendation**: [suggested approach]

Please provide guidance on how to proceed.
        ]]>
      </format>
    </pattern>
    
    <pattern name="progress_updates">
      <description>How to provide progress updates</description>
      <format>
        <![CDATA[
**Progress Update**: [task name]
**Status**: [completed/in progress/blocked]
**Details**: [what was accomplished]
**Next Steps**: [immediate next actions]
**Blockers**: [any issues preventing progress]
        ]]>
      </format>
    </pattern>
    
    <pattern name="completion_report">
      <description>How to report task completion</description>
      <format>
        <![CDATA[
**Task Completed**: [task name]
**Verification**: [how it was tested]
**Quality Checks**: [linting, tests, coverage]
**Documentation**: [what was documented]
**Ready for**: [next phase - verification/deployment]
        ]]>
      </format>
    </pattern>
  </interaction_patterns>

  <escalation_paths>
    <path name="technical_clarification">
      <steps>
        <step>Attempt to resolve using existing documentation</step>
        <step>Check similar implementations in codebase</step>
        <step>Request clarification from Task Planner Agent</step>
        <step>Escalate to Technical Design Agent if needed</step>
        <step>Escalate to Orchestrator for architectural decisions</step>
      </steps>
    </path>
    
    <path name="quality_gates">
      <steps>
        <step>Identify specific quality issue</step>
        <step>Attempt to resolve within scope</step>
        <step>Document the issue and attempted solutions</step>
        <step>Request guidance from appropriate agent</step>
        <step>Escalate if resolution requires architectural changes</step>
      </steps>
    </path>
  </escalation_paths>

  <documentation_standards>
    <standard name="code_comments">
      <requirements>
        <requirement>Add docstrings to all public functions</requirement>
        <requirement>Document complex algorithms</requirement>
        <requirement>Explain non-obvious business logic</requirement>
        <requirement>Include usage examples for complex functions</requirement>
      </requirements>
      <template>
        <![CDATA[
def calculate_power_consumption(voltage: float, current: float) -> float:
    """
    Calculate power consumption in watts.
    
    Args:
        voltage: Voltage in volts (must be positive)
        current: Current in amperes (must be positive)
    
    Returns:
        Power consumption in watts
        
    Example:
        >>> calculate_power_consumption(12.0, 5.0)
        60.0
        
    Raises:
        ValueError: If voltage or current is negative
    """
    if voltage < 0 or current < 0:
        raise ValueError("Voltage and current must be positive")
    return voltage * current
        ]]>
      </template>
    </standard>
    
    <standard name="api_documentation">
      <requirements>
        <requirement>Document all endpoints</requirement>
        <requirement>Include request/response examples</requirement>
        <requirement>Document error responses</requirement>
        <requirement>Include authentication requirements</requirement>
      </requirements>
    </standard>
    
    <standard name="component_documentation">
      <requirements>
        <requirement>Document component props</requirement>
        <requirement>Include usage examples</requirement>
        <requirement>Document state management</requirement>
        <requirement>Include styling information</requirement>
      </requirements>
    </standard>
  </documentation_standards>

  <handover_procedures>
    <procedure name="feature_completion">
      <steps>
        <step>Verify all tests pass</step>
        <step>Run quality checks (linting, type checking)</step>
        <step>Update documentation</step>
        <step>Create summary of changes</step>
        <step>Prepare for verification phase</step>
      </steps>
      <checklist>
        <item>All acceptance criteria met</item>
        <item>Tests have 100% pass rate</item>
        <item>Code quality checks pass</item>
        <item>Documentation is complete</item>
        <item>No TODO comments remain</item>
      </checklist>
    </procedure>
    
    <procedure name="bug_fix_handover">
      <steps>
        <step>Document root cause</step>
        <step>Verify fix addresses issue</step>
        <step>Add regression tests</step>
        <step>Update relevant documentation</step>
        <step>Provide testing instructions</step>
      </steps>
    </procedure>
  </handover_procedures>

  <communication_templates>
    <template name="daily_standup">
      <format>
        <![CDATA[
**Yesterday**: [completed tasks]
**Today**: [planned tasks]
**Blockers**: [any impediments]
**Need Help With**: [specific assistance needed]
        ]]>
      </format>
    </template>
    
    <template name="code_review_request">
      <format>
        <![CDATA[
**Feature**: [brief description]
**Changes**: [summary of modifications]
**Testing**: [how it was tested]
**Documentation**: [what was documented]
**Ready for Review**: [yes/no and any specific areas]
        ]]>
      </format>
    </template>
    
    <template name="issue_report">
      <format>
        <![CDATA[
**Issue**: [brief description]
**Steps to Reproduce**: [detailed steps]
**Expected Behavior**: [what should happen]
**Actual Behavior**: [what actually happens]
**Environment**: [backend/frontend/database]
**Impact**: [severity and scope]
        ]]>
      </format>
    </template>
  </communication_templates>

  <quality_assurance>
    <checklist name="communication_quality">
      <item>Clear and concise messages</item>
      <item>Specific technical details</item>
      <item>Actionable next steps</item>
      <item>Proper context provided</item>
      <item>Appropriate escalation level</item>
    </checklist>
    
    <checklist name="documentation_quality">
      <item>Complete API documentation</item>
      <item>Code comments for complex logic</item>
      <item>Usage examples provided</item>
      <item>Error scenarios documented</item>
      <item>Setup instructions included</item>
    </checklist>
  </quality_assurance>
</communication_guidelines>