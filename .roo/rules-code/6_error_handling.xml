<error_handling_guide>
  <error_categories>
    <category name="test_failures">
      <description>Issues with failing tests during TDD</description>
      <common_scenarios>
        <scenario>
          <symptom>Test fails with assertion error</symptom>
          <diagnosis>
            <step>Check test assertion matches expected behavior</step>
            <step>Verify test data setup is correct</step>
            <step>Ensure mock configurations are accurate</step>
          </diagnosis>
          <resolution>
            <action>Update test assertion to match actual behavior</action>
            <action>Fix test data setup</action>
            <action>Adjust mock return values</action>
          </resolution>
        </scenario>
        
        <scenario>
          <symptom>Async test fails with timeout</symptom>
          <diagnosis>
            <step>Check for missing async/await keywords</step>
            <step>Verify test database setup</step>
            <step>Check for hanging async operations</step>
          </diagnosis>
          <resolution>
            <action>Add proper async/await syntax</action>
            <action>Use pytest-asyncio fixtures</action>
            <action>Increase timeout for slow operations</action>
          </resolution>
        </scenario>
      </common_scenarios>
    </category>
    
    <category name="type_errors">
      <description>TypeScript/MyPy type checking issues</description>
      <common_scenarios>
        <scenario>
          <symptom>MyPy reports type errors</symptom>
          <diagnosis>
            <step>Check function signatures and return types</step>
            <step>Verify generic type parameters</step>
            <step>Check for missing type annotations</step>
          </diagnosis>
          <resolution>
            <action>Add proper type annotations</action>
            <action>Use TypeVar for generics</action>
            <action>Fix import statements</action>
          </resolution>
        </scenario>
        
        <scenario>
          <symptom>TypeScript compilation errors</symptom>
          <diagnosis>
            <step>Check interface definitions</step>
            <step>Verify prop types in React components</step>
            <step>Check for any/unknown types</step>
          </diagnosis>
          <resolution>
            <action>Add proper interface definitions</action>
            <action>Use proper React prop types</action>
            <action>Replace any with specific types</action>
          </resolution>
        </scenario>
      </common_scenarios>
    </category>
    
    <category name="database_errors">
      <description>Database-related issues</description>
      <common_scenarios>
        <scenario>
          <symptom>Migration fails</symptom>
          <diagnosis>
            <step>Check migration file syntax</step>
            <step>Verify database connection</step>
            <step>Check for conflicting migrations</step>
          </diagnosis>
          <resolution>
            <action>Fix migration syntax</action>
            <action>Check database URL configuration</action>
            <action>Resolve migration conflicts</action>
          </resolution>
        </scenario>
        
        <scenario>
          <symptom>SQLAlchemy relationship errors</symptom>
          <diagnosis>
            <step>Check relationship definitions</step>
            <step>Verify foreign key constraints</step>
            <step>Check for circular dependencies</step>
          </diagnosis>
          <resolution>
            <action>Fix relationship configuration</action>
            <action>Add proper foreign keys</action>
            <action>Use lazy loading appropriately</action>
          </resolution>
        </scenario>
      </common_scenarios>
    </category>
    
    <category name="frontend_errors">
      <description>React and frontend-specific issues</description>
      <common_scenarios>
        <scenario>
          <symptom>React Query cache issues</symptom>
          <diagnosis>
            <step>Check query keys for uniqueness</step>
            <step>Verify cache invalidation logic</step>
            <step>Check for stale data</step>
          </diagnosis>
          <resolution>
            <action>Use unique and consistent query keys</action>
            <action>Implement proper cache invalidation</action>
            <action>Use staleTime and cacheTime appropriately</action>
          </resolution>
        </scenario>
        
        <scenario>
          <symptom>Form validation errors</symptom>
          <diagnosis>
            <step>Check validation schema</step>
            <step>Verify form field names</step>
            <step>Check error message display</step>
          </diagnosis>
          <resolution>
            <action>Fix validation rules</action>
            <action>Ensure field names match schema</action>
            <action>Display user-friendly error messages</action>
          </resolution>
        </scenario>
      </common_scenarios>
    </category>
  </error_categories>

  <debugging_strategies>
    <strategy name="systematic_debugging">
      <steps>
        <step>Reproduce the error consistently</step>
        <step>Isolate the failing component</step>
        <step>Add logging/debugging statements</step>
        <step>Check recent changes</step>
        <step>Verify environment setup</step>
      </steps>
    </strategy>
    
    <strategy name="test_debugging">
      <steps>
        <step>Run specific failing test with -v flag</step>
        <step>Use pytest --pdb for interactive debugging</step>
        <step>Check test fixtures and setup</step>
        <step>Compare with working tests</step>
      </steps>
    </strategy>
    
    <strategy name="frontend_debugging">
      <steps>
        <step>Use React DevTools to inspect component state</step>
        <step>Check network tab for API calls</step>
        <step>Use console.log for state changes</step>
        <step>Verify React Query dev tools</step>
      </steps>
    </strategy>
  </debugging_strategies>

  <recovery_procedures>
    <procedure name="rollback_changes">
      <when>When changes break existing functionality</when>
      <steps>
        <step>Identify the breaking change</step>
        <step>Create a backup branch</step>
        <step>Revert specific commits</step>
        <step>Verify functionality is restored</step>
        <step>Re-implement with fixes</step>
      </steps>
    </procedure>
    
    <procedure name="database_recovery">
      <when>When database migrations fail</when>
      <steps>
        <step>Check migration history</step>
        <step>Downgrade to last working version</step>
        <step>Fix migration file</step>
        <step>Test migration locally</step>
        <step>Apply fixed migration</step>
      </steps>
    </procedure>
  </recovery_procedures>

  <prevention_strategies>
    <strategy name="code_quality">
      <practices>
        <practice>Write tests before implementation</practice>
        <practice>Run tests frequently during development</practice>
        <practice>Use pre-commit hooks</practice>
        <practice>Perform code reviews</practice>
      </practices>
    </strategy>
    
    <strategy name="documentation">
      <practices>
        <practice>Document complex logic</practice>
        <practice>Add inline comments for non-obvious code</practice>
        <practice>Update README files</practice>
        <practice>Create troubleshooting guides</practice>
      </practices>
    </strategy>
  </prevention_strategies>

  <escalation_procedures>
    <procedure name="seek_help">
      <when>When stuck for more than 30 minutes</when>
      <steps>
        <step>Document the issue clearly</step>
        <step>Check existing documentation</step>
        <step>Ask for clarification from Technical Design Agent</step>
        <step>Escalate to Orchestrator if needed</step>
      </steps>
    </procedure>
  </escalation_procedures>
</error_handling_guide>