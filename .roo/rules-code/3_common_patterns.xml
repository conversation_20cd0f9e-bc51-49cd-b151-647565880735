<common_patterns>
  <backend_patterns>
    <pattern name="service_layer_pattern">
      <description>Standard service layer implementation</description>
      <structure>
        <![CDATA[
class CircuitService:
    def __init__(self, repository: CircuitRepository):
        self.repository = repository
    
    async def create_circuit(self, data: CircuitCreate) -> CircuitRead:
        """Create a new circuit with validation."""
        # Business logic here
        return await self.repository.create(data)
    
    async def get_circuit(self, id: int) -> CircuitRead:
        """Retrieve a circuit by ID."""
        circuit = await self.repository.get_by_id(id)
        if not circuit:
            raise NotFoundError(f"Circuit {id} not found")
        return circuit
        ]]>
      </structure>
      <testing_approach>
        <test>Mock repository layer</test>
        <test>Test business logic independently</test>
        <test>Verify error handling</test>
      </testing_approach>
    </pattern>
    
    <pattern name="repository_pattern">
      <description>Repository pattern for data access</description>
      <structure>
        <![CDATA[
class CircuitRepository:
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def create(self, data: CircuitCreate) -> Circuit:
        circuit = Circuit(**data.model_dump())
        self.session.add(circuit)
        await self.session.commit()
        await self.session.refresh(circuit)
        return circuit
    
    async def get_by_id(self, id: int) -> Optional[Circuit]:
        result = await self.session.execute(
            select(Circuit).where(Circuit.id == id)
        )
        return result.scalar_one_or_none()
        ]]>
      </structure>
    </pattern>
    
    <pattern name="validation_schema">
      <description>Pydantic schemas for validation</description>
      <structure>
        <![CDATA[
class CircuitCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    voltage: float = Field(..., gt=0, description="Voltage in volts")
    current: float = Field(..., gt=0, description="Current in amperes")
    
    @field_validator('voltage')
    def validate_voltage(cls, v):
        if v > 1000:
            raise ValueError('Voltage too high for standard circuits')
        return v
        ]]>
      </structure>
    </pattern>
    
    <pattern name="dependency_injection">
      <description>FastAPI dependency injection patterns</description>
      <structure>
        <![CDATA[
async def get_circuit_repository(
    session: AsyncSession = Depends(get_session)
) -> CircuitRepository:
    return CircuitRepository(session)

async def get_circuit_service(
    repository: CircuitRepository = Depends(get_circuit_repository)
) -> CircuitService:
    return CircuitService(repository)
        ]]>
      </structure>
    </pattern>
  </backend_patterns>

  <frontend_patterns>
    <pattern name="api_client_hooks">
      <description>React Query hooks for API calls</description>
      <structure>
        <![CDATA[
// API client
const apiClient = {
  circuits: {
    getAll: () => api.get<Circuit[]>('/circuits'),
    getById: (id: number) => api.get<Circuit>(`/circuits/${id}`),
    create: (data: CircuitCreate) => api.post<Circuit>('/circuits', data),
    update: (id: number, data: CircuitUpdate) => api.put<Circuit>(`/circuits/${id}`, data),
    delete: (id: number) => api.delete<void>(`/circuits/${id}`),
  },
};

// React Query hooks
export function useCircuits() {
  return useQuery({
    queryKey: ['circuits'],
    queryFn: apiClient.circuits.getAll,
  });
}

export function useCircuit(id: number) {
  return useQuery({
    queryKey: ['circuits', id],
    queryFn: () => apiClient.circuits.getById(id),
    enabled: !!id,
  });
}

export function useCreateCircuit() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: apiClient.circuits.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['circuits'] });
    },
  });
}
        ]]>
      </structure>
    </pattern>
    
    <pattern name="form_component">
      <description>Reusable form component pattern</description>
      <structure>
        <![CDATA[
interface CircuitFormProps {
  initialData?: Circuit;
  onSubmit: (data: CircuitCreate) => Promise<void>;
  onCancel?: () => void;
}

export function CircuitForm({ initialData, onSubmit, onCancel }: CircuitFormProps) {
  const form = useForm<CircuitCreate>({
    resolver: zodResolver(circuitSchema),
    defaultValues: initialData || {
      name: '',
      voltage: 0,
      current: 0,
    },
  });

  const { mutate: submitForm, isPending } = useMutation({
    mutationFn: onSubmit,
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit((data) => submitForm(data))}>
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Circuit Name</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="voltage"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Voltage (V)</FormLabel>
              <FormControl>
                <Input type="number" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <div className="flex gap-2">
          <Button type="submit" disabled={isPending}>
            {isPending ? 'Saving...' : 'Save'}
          </Button>
          {onCancel && (
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
          )}
        </div>
      </form>
    </Form>
  );
}
        ]]>
      </structure>
    </pattern>
    
    <pattern name="data_table">
      <description>Reusable data table with sorting and filtering</description>
      <structure>
        <![CDATA[
interface DataTableProps<T> {
  data: T[];
  columns: ColumnDef<T>[];
  onRowClick?: (row: T) => void;
  loading?: boolean;
}

export function DataTable<T>({ data, columns, onRowClick, loading }: DataTableProps<T>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    state: {
      sorting,
      columnFilters,
    },
  });

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <TableHead key={header.id}>
                  {header.isPlaceholder
                    ? null
                    : flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                </TableHead>
              ))}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {loading ? (
            <TableRow>
              <TableCell colSpan={columns.length} className="text-center">
                Loading...
              </TableCell>
            </TableRow>
          ) : (
            table.getRowModel().rows.map((row) => (
              <TableRow
                key={row.id}
                onClick={() => onRowClick?.(row.original)}
                className={cn(onRowClick && "cursor-pointer")}
              >
                {row.getVisibleCells().map((cell) => (
                  <TableCell key={cell.id}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
}
        ]]>
      </structure>
    </pattern>
    
    <pattern name="error_boundary">
      <description>Error boundary for React components</description>
      <structure>
        <![CDATA[
interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

class ErrorBoundary extends Component<PropsWithChildren, ErrorBoundaryState> {
  state: ErrorBoundaryState = {
    hasError: false,
    error: null,
  };

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex flex-col items-center justify-center p-8">
          <h2 className="text-lg font-semibold">Something went wrong</h2>
          <p className="text-muted-foreground">{this.state.error?.message}</p>
          <Button onClick={() => this.setState({ hasError: false, error: null })}>
            Try again
          </Button>
        </div>
      );
    }

    return this.props.children;
  }
}
        ]]>
      </structure>
    </pattern>
  </frontend_patterns>

  <testing_patterns>
    <pattern name="backend_test_structure">
      <description>Standard pytest structure for backend tests</description>
      <structure>
        <![CDATA[
class TestCircuitService:
    @pytest.fixture
    def service(self, mock_repository):
        return CircuitService(mock_repository)
    
    @pytest.fixture
    def mock_repository(self):
        return Mock(spec=CircuitRepository)
    
    async def test_create_circuit_success(self, service, mock_repository):
        # Arrange
        data = CircuitCreate(name="Test Circuit", voltage=12.0, current=5.0)
        expected = Circuit(id=1, **data.model_dump())
        mock_repository.create.return_value = expected
        
        # Act
        result = await service.create_circuit(data)
        
        # Assert
        assert result == expected
        mock_repository.create.assert_called_once_with(data)
    
    async def test_get_circuit_not_found(self, service, mock_repository):
        mock_repository.get_by_id.return_value = None
        
        with pytest.raises(NotFoundError):
            await service.get_circuit(999)
        ]]>
      </structure>
    </pattern>
    
    <pattern name="frontend_test_structure">
      <description>React Testing Library patterns</description>
      <structure>
        <![CDATA[
describe('CircuitForm', () => {
  it('should render form fields', () => {
    render(<CircuitForm onSubmit={vi.fn()} />);
    
    expect(screen.getByLabelText(/circuit name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/voltage/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/current/i)).toBeInTheDocument();
  });

  it('should validate required fields', async () => {
    const user = userEvent.setup();
    render(<CircuitForm onSubmit={vi.fn()} />);
    
    const submitButton = screen.getByRole('button', { name: /save/i });
    await user.click(submitButton);
    
    expect(await screen.findByText(/name is required/i)).toBeInTheDocument();
  });

  it('should submit valid form data', async () => {
    const user = userEvent.setup();
    const mockSubmit = vi.fn();
    render(<CircuitForm onSubmit={mockSubmit} />);
    
    await user.type(screen.getByLabelText(/circuit name/i), 'Test Circuit');
    await user.type(screen.getByLabelText(/voltage/i), '12');
    await user.type(screen.getByLabelText(/current/i), '5');
    
    const submitButton = screen.getByRole('button', { name: /save/i });
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(mockSubmit).toHaveBeenCalledWith({
        name: 'Test Circuit',
        voltage: 12,
        current: 5,
      });
    });
  });
});
        ]]>
      </structure>
    </pattern>
  </testing_patterns>

  <integration_patterns>
    <pattern name="database_migration">
      <description>Creating and applying database migrations</description>
      <steps>
        <step>Create migration file</step>
        <step>Define upgrade and downgrade functions</step>
        <step>Test migration locally</step>
        <step>Apply to development database</step>
      </steps>
      <example>
        <![CDATA[
"""Add circuit table

Revision ID: abc123
Revises: def456
Create Date: 2024-01-01 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

def upgrade() -> None:
    op.create_table(
        'circuits',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('voltage', sa.Float(), nullable=False),
        sa.Column('current', sa.Float(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_circuits_name', 'circuits', ['name'])

def downgrade() -> None:
    op.drop_index('ix_circuits_name', table_name='circuits')
    op.drop_table('circuits')
        ]]>
      </example>
    </pattern>
    
    <pattern name="api_integration">
      <description>Connecting frontend to new backend endpoints</description>
      <steps>
        <step>Create/update API client</step>
        <step>Create React Query hooks</step>
        <step>Update frontend components</step>
        <step>Test integration</step>
      </steps>
    </pattern>
  </integration_patterns>
</common_patterns>