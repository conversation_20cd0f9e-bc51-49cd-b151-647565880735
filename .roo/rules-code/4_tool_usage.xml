<tool_usage_guide>
  <tool_priorities>
    <priority level="1">
      <tool>read_file</tool>
      <when>Always start by reading relevant files</when>
      <why>Understand existing code and patterns before making changes</why>
    </priority>
    
    <priority level="2">
      <tool>write_to_file</tool>
      <when>Create new files for tests, models, services, components</when>
      <why>Start with test files first (TDD approach)</why>
    </priority>
    
    <priority level="3">
      <tool>apply_diff</tool>
      <when>Modify existing files</when>
      <why>Surgical changes to existing code</why>
    </priority>
    
    <priority level="4">
      <tool>execute_command</tool>
      <when>Run tests, linting, and quality checks</when>
      <why>Ensure code quality and test compliance</why>
    </priority>
  </tool_priorities>

  <backend_tool_usage>
    <tool name="pytest">
      <purpose>Run Python tests with comprehensive reporting</purpose>
      <commands>
        <command>cd server && uv run pytest tests/ -v --cov=src --cov-report=term-missing</command>
        <command>cd server && uv run pytest tests/test_specific_file.py::TestClass::test_method -v</command>
        <command>cd server && uv run pytest tests/ -m "not integration" --html=test-report.html</command>
      </commands>
      <usage_notes>
        <note>Always run tests before and after implementation</note>
        <note>Use specific test targeting during development</note>
        <note>Ensure 100% pass rate before completion</note>
      </usage_notes>
    </tool>
    
    <tool name="mypy">
      <purpose>Type checking for Python code</purpose>
      <commands>
        <command>cd server && uv run mypy src/ --show-error-codes</command>
        <command>cd server && uv run mypy src/modules/electrical/ --show-error-codes</command>
      </commands>
      <usage_notes>
        <note>Zero tolerance for type errors</note>
        <note>Fix all type issues before committing</note>
      </usage_notes>
    </tool>
    
    <tool name="ruff">
      <purpose>Python linting and formatting</purpose>
      <commands>
        <command>cd server && uv run ruff format .</command>
        <command>cd server && uv run ruff check . --fix</command>
        <command>cd server && uv run ruff format . --check</command>
      </commands>
      <usage_notes>
        <note>Format code automatically</note>
        <note>Fix linting issues with --fix flag</note>
        <note>Ensure no warnings remain</note>
      </usage_notes>
    </tool>
    
    <tool name="alembic">
      <purpose>Database migrations</purpose>
      <commands>
        <command>cd server/src && uv run alembic revision --autogenerate -m "Add circuit table"</command>
        <command>cd server/src && uv run alembic upgrade head</command>
        <command>cd server/src && uv run alembic downgrade -1</command>
      </commands>
      <usage_notes>
        <note>Always test migrations locally first</note>
        <note>Create descriptive migration messages</note>
        <note>Test downgrade as well as upgrade</note>
      </usage_notes>
    </tool>
  </backend_tool_usage>

  <frontend_tool_usage>
    <tool name="vitest">
      <purpose>Run frontend unit tests</purpose>
      <commands>
        <command>cd client && pnpm vitest --run</command>
        <command>cd client && pnpm vitest Component.test.tsx --run</command>
        <command>cd client && pnpm vitest --coverage --run</command>
      </commands>
      <usage_notes>
        <note>Run tests in watch mode during development</note>
        <note>Ensure 100% test pass rate</note>
        <note>Check coverage reports</note>
      </usage_notes>
    </tool>
    
    <tool name="typescript">
      <purpose>Type checking for TypeScript</purpose>
      <commands>
        <command>cd client && pnpm tsc --noEmit</command>
        <command>cd client && pnpm tsc --noEmit --watch</command>
      </commands>
      <usage_notes>
        <note>Zero tolerance for type errors</note>
        <note>Use strict mode</note>
      </usage_notes>
    </tool>
    
    <tool name="eslint">
      <purpose>JavaScript/TypeScript linting</purpose>
      <commands>
        <command>cd client && pnpm next lint --fix</command>
        <command>cd client && pnpm next lint --dir src/components</command>
      </commands>
      <usage_notes>
        <note>Fix all linting issues</note>
        <note>Use --fix for automatic fixes</note>
      </usage_notes>
    </tool>
    
    <tool name="prettier">
      <purpose>Code formatting</purpose>
      <commands>
        <command>cd client && pnpm prettier --write "src/**/*.{ts,tsx}"</command>
        <command>cd client && pnpm prettier --check "src/**/*.{ts,tsx}"</command>
      </commands>
      <usage_notes>
        <note>Format code consistently</note>
        <note>Use pre-commit hooks</note>
      </usage_notes>
    </tool>
    
    <tool name="playwright">
      <purpose>End-to-end testing</purpose>
      <commands>
        <command>cd client && pnpm playwright test</command>
        <command>cd client && pnpm playwright test tests/e2e/circuit-flow.spec.ts</command>
        <command>cd client && pnpm playwright test --ui</command>
      </commands>
      <usage_notes>
        <note>Write e2e tests for critical user flows</note>
        <note>Use --ui for debugging</note>
        <note>Test on multiple browsers</note>
      </usage_notes>
    </tool>
  </frontend_tool_usage>

  <development_workflow>
    <phase name="red_phase">
      <description>Write failing tests</description>
      <steps>
        <step>Create test file with failing test</step>
        <step>Run test to confirm it fails</step>
        <step>Verify test covers requirement</step>
      </steps>
    </phase>
    
    <phase name="green_phase">
      <description>Make test pass with minimal code</description>
      <steps>
        <step>Implement minimal code to pass test</step>
        <step>Run test to confirm it passes</step>
        <step>Run all related tests</step>
      </steps>
    </phase>
    
    <phase name="refactor_phase">
      <description>Improve code quality</description>
      <steps>
        <step>Refactor for better structure</step>
        <step>Run tests to ensure no regression</step>
        <step>Run quality checks</step>
      </steps>
    </phase>
  </development_workflow>

  <quality_gates>
    <gate name="pre_commit">
      <checks>
        <check>All tests pass</check>
        <check>Type checking passes</check>
        <check>Linting passes</check>
        <check>Code is formatted</check>
      </checks>
    </gate>
    
    <gate name="pre_push">
      <checks>
        <check>Full test suite passes</check>
        <check>Coverage targets met</check>
        <check>Integration tests pass</check>
        <check>E2E tests pass</check>
      </checks>
    </gate>
  </quality_gates>

  <troubleshooting>
    <issue name="test_failure">
      <symptoms>Tests fail unexpectedly</symptoms>
      <solutions>
        <solution>Check test setup and fixtures</solution>
        <solution>Verify mock configurations</solution>
        <solution>Check for race conditions</solution>
        <solution>Review test data setup</solution>
      </solutions>
    </issue>
    
    <issue name="type_errors">
      <symptoms>TypeScript/MyPy errors</symptoms>
      <solutions>
        <solution>Check type definitions</solution>
        <solution>Verify import statements</solution>
        <solution>Check generic type parameters</solution>
        <solution>Review interface definitions</solution>
      </solutions>
    </issue>
    
    <issue name="performance_issues">
      <symptoms>Slow tests or build times</symptoms>
      <solutions>
        <solution>Check for inefficient queries</solution>
        <solution>Optimize component re-renders</solution>
        <solution>Use proper React Query caching</solution>
        <solution>Review bundle size</solution>
      </solutions>
    </issue>
  </troubleshooting>
</tool_usage_guide>