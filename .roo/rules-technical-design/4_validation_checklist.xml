<validation_checklist>
  <pre_design_validation>
    <category name="requirements_clarity">
      <item>User request is clearly understood</item>
      <item>Business value is articulated</item>
      <item>Functional requirements are identified</item>
      <item>Non-functional requirements are specified</item>
      <item>Constraints and limitations are documented</item>
    </category>
    
    <category name="context_gathering">
      <item>README.md has been read and understood</item>
      <item>All relevant documentation has been reviewed</item>
      <item>Existing system context is understood</item>
      <item>Integration points are identified</item>
    </category>
  </pre_design_validation>

  <design_validation>
    <category name="architectural_alignment">
      <item>Design follows 5-layer architecture</item>
      <item>CRUD factory pattern applied where appropriate</item>
      <item>Unified error handling incorporated</item>
      <item>SOLID principles applied throughout</item>
      <item>DRY principle followed</item>
    </category>
    
    <category name="standards_compliance">
      <item>Zero tolerance policies addressed</item>
      <item>Type safety requirements met</item>
      <item>Security best practices included</item>
      <item>Testing strategy defined</item>
      <item>Documentation requirements specified</item>
    </category>
    
    <category name="technical_completeness">
      <item>Data models are comprehensive</item>
      <item>API contracts are well-defined</item>
      <item>Database schema changes identified</item>
      <item>Frontend component structure defined</item>
      <item>Integration points documented</item>
    </category>
    
    <category name="quality_assurance">
      <item>Performance considerations addressed</item>
      <item>Scalability requirements met</item>
      <item>Error handling is comprehensive</item>
      <item>Security vulnerabilities addressed</item>
      <item>Edge cases considered</item>
    </category>
  </design_validation>

  <handover_validation>
    <category name="task_planner_ready">
      <item>Design is complete and unambiguous</item>
      <item>No implementation details included</item>
      <item>Clear handoff points identified</item>
      <item>Dependencies documented</item>
      <item>Success criteria defined</item>
    </category>
  </handover_validation>

  <design_quality_gates>
    <gate name="requirements_gate">
      <criteria>
        <criterion>All requirements are clearly defined</criterion>
        <criterion>Requirements are testable</criterion>
        <criterion>Requirements align with project goals</criterion>
      </criteria>
      <validation_method>Review against docs/requirements.md</validation_method>
    </gate>
    
    <gate name="architecture_gate">
      <criteria>
        <criterion>Design follows 5-layer architecture</criterion>
        <criterion>Patterns are applied correctly</criterion>
        <criterion>Integration points are identified</criterion>
      </criteria>
      <validation_method>Architecture review checklist</validation_method>
    </gate>
    
    <gate name="standards_gate">
      <criteria>
        <criterion>All documented standards are addressed</criterion>
        <criterion>Security requirements are met</criterion>
        <criterion>Testing approach is defined</criterion>
      </criteria>
      <validation_method>Standards compliance review</validation_method>
    </gate>
  </design_quality_gates>

  <handover_template>
    <section name="design_summary">
      <content>Executive summary of the feature design</content>
    </section>
    
    <section name="technical_specification">
      <content>Complete technical design document</content>
    </section>
    
    <section name="handoff_notes">
      <notes>
        <note>Key architectural decisions and rationale</note>
        <note>Integration points requiring special attention</note>
        <note>Security considerations</note>
        <note>Performance requirements</note>
        <note>Testing strategy highlights</note>
      </notes>
    </section>
    
    <section name="next_phase_guidance">
      <content>Clear direction for Task Planner Agent</content>
    </section>
  </handover_template>
</validation_checklist>