<design_examples>
  <example name="user_authentication_system">
    <description>Complete technical design for JWT-based authentication</description>
    <context>
      <user_request>Implement secure user authentication with JWT tokens and refresh tokens</user_request>
      <project_context>Existing user model, need to add authentication layer</project_context>
    </context>
    
    <design_document>
      <executive_summary>
        Design a secure authentication system using JWT access tokens and refresh tokens. The system will provide secure login/logout functionality with automatic token refresh and support for multiple device sessions.
      </executive_summary>
      
      <requirements>
        <functional_requirements>
          <requirement>User login with email/password</requirement>
          <requirement>JWT token generation on successful login</requirement>
          <requirement>Token refresh mechanism</requirement>
          <requirement>Secure logout functionality</requirement>
          <requirement>Multiple device session support</requirement>
        </functional_requirements>
        <non_functional_requirements>
          <requirement>Tokens expire after 15 minutes (access) and 7 days (refresh)</requirement>
          <requirement>Support 1000+ concurrent users</requirement>
          <requirement>OWASP security standards compliance</requirement>
        </non_functional_requirements>
      </requirements>
      
      <architecture>
        <backend_design>
          <api_layer>
            <endpoints>
              <endpoint method="POST" path="/api/v1/auth/login">
                <description>Authenticate user and return tokens</description>
                <request_schema>UserLoginRequest</request_schema>
                <response_schema>TokenResponse</response_schema>
              </endpoint>
              <endpoint method="POST" path="/api/v1/auth/refresh">
                <description>Refresh access token using refresh token</description>
                <request_schema>TokenRefreshRequest</request_schema>
                <response_schema>TokenResponse</response_schema>
              </endpoint>
              <endpoint method="POST" path="/api/v1/auth/logout">
                <description>Invalidate user session</description>
                <request_schema>LogoutRequest</request_schema>
                <response_schema>SuccessResponse</response_schema>
              </endpoint>
            </endpoints>
          </api_layer>
          
          <service_layer>
            <services>
              <service name="AuthenticationService">
                <methods>
                  <method name="authenticate_user">Validate credentials and return user</method>
                  <method name="generate_tokens">Create JWT access and refresh tokens</method>
                  <method name="refresh_tokens">Generate new tokens from refresh token</method>
                  <method name="invalidate_session">Remove user session</method>
                </methods>
              </service>
            </services>
          </service_layer>
          
          <repository_layer>
            <repositories>
              <repository name="UserRepository">
                <methods>
                  <method name="get_by_email">Find user by email</method>
                  <method name="update_last_login">Update user's last login timestamp</method>
                </methods>
              </repository>
              <repository name="RefreshTokenRepository">
                <methods>
                  <method name="create">Store new refresh token</method>
                  <method name="get_by_token">Find refresh token by token value</method>
                  <method name="revoke_by_user">Revoke all tokens for user</method>
                </methods>
              </repository>
            </repositories>
          </repository_layer>
          
          <model_layer>
            <tables>
              <table name="refresh_tokens">
                <columns>
                  <column name="id" type="INTEGER" primary_key="true"/>
                  <column name="user_id" type="INTEGER" foreign_key="users.id"/>
                  <column name="token" type="VARCHAR(255)" unique="true"/>
                  <column name="expires_at" type="TIMESTAMP"/>
                  <column name="created_at" type="TIMESTAMP"/>
                  <column name="revoked" type="BOOLEAN" default="false"/>
                </columns>
              </table>
            </tables>
          </model_layer>
        </backend_design>
        
        <frontend_design>
          <components>
            <component name="LoginForm">
              <props>
                <prop name="onSuccess" type="function">Callback on successful login</prop>
                <prop name="redirectPath" type="string">Path to redirect after login</prop>
              </props>
            </component>
            <component name="AuthProvider">
              <description>Context provider for authentication state</description>
              <state>
                <item>user: User | null</item>
                <item>isAuthenticated: boolean</item>
                <item>login: (credentials) => Promise</item>
                <item>logout: () => Promise</item>
              </state>
            </component>
          </components>
          
          <state_management>
            <store name="authStore">
              <state>
                <item>user: User | null</item>
                <item>accessToken: string | null</item>
                <item>refreshToken: string | null</item>
              </state>
              <actions>
                <action name="setAuth">Set authentication state</action>
                <action name="clearAuth">Clear authentication state</action>
                <action name="refreshToken">Refresh access token</action>
              </actions>
            </store>
          </state_management>
        </frontend_design>
      </architecture>
      
      <technical_specifications>
        <api_specification>
          <schema name="UserLoginRequest">
            <fields>
              <field name="email" type="EmailStr" required="true"/>
              <field name="password" type="SecretStr" required="true"/>
            </fields>
          </schema>
          
          <schema name="TokenResponse">
            <fields>
              <field name="access_token" type="str"/>
              <field name="refresh_token" type="str"/>
              <field name="token_type" type="str" default="bearer"/>
              <field name="expires_in" type="int"/>
            </fields>
          </schema>
        </api_specification>
        
        <security_design>
          <token_strategy>
            <access_token>
              <algorithm>HS256</algorithm>
              <expiry>15 minutes</expiry>
              <claims>user_id, email, roles</claims>
            </access_token>
            <refresh_token>
              <algorithm>HS256</algorithm>
              <expiry>7 days</expiry>
              <claims>user_id, session_id</claims>
            </refresh_token>
          </token_strategy>
        </security_design>
      </technical_specifications>
      
      <integration_plan>
        <database_changes>
          <migration>Create refresh_tokens table</migration>
          <migration>Add last_login_at to users table</migration>
        </database_changes>
        
        <frontend_integration>
          <update>Add AuthProvider to root layout</update>
          <update>Add login/logout buttons to navigation</update>
        </frontend_integration>
      </integration_plan>
    </design_document>
  </example>
  
  <example name="project_management_feature">
    <description>CRUD operations for electrical projects</description>
    <context>
      <user_request>Add project management functionality for electrical design projects</user_request>
      <project_context>Need to manage electrical projects with specifications and drawings</project_context>
    </context>
    
    <design_document>
      <executive_summary>
        Design a comprehensive project management system for electrical design projects, including project creation, editing, and management with support for specifications and file attachments.
      </executive_summary>
      
      <requirements>
        <functional_requirements>
          <requirement>Create new electrical projects</requirement>
          <requirement>Edit project details</requirement>
          <requirement>List and search projects</requirement>
          <requirement>Delete projects (soft delete)</requirement>
          <requirement>Upload and manage project specifications</requirement>
        </functional_requirements>
      </requirements>
      
      <architecture>
        <backend_design>
          <crud_application>
            <entity>Project</entity>
            <factory_config>
              <code language="python">
create_simple_crud_router(
    entity_name="project",
    entity_name_plural="projects",
    create_schema=ProjectCreate,
    read_schema=ProjectRead,
    update_schema=ProjectUpdate,
    list_response_schema=ProjectListResponse,
    service_class=ProjectService,
    service_dependency=get_project_service,
    id_type=int,
    searchable_fields=["name", "description", "client_name"],
    sortable_fields=["name", "created_at", "updated_at", "status"],
)
              </code>
            </factory_config>
          </crud_application>
          
          <data_model>
            <table name="projects">
              <columns>
                <column name="id" type="INTEGER" primary_key="true"/>
                <column name="name" type="VARCHAR(255)" nullable="false"/>
                <column name="description" type="TEXT"/>
                <column name="client_name" type="VARCHAR(255)"/>
                <column name="status" type="VARCHAR(50)" default="'draft'"/>
                <column name="created_at" type="TIMESTAMP"/>
                <column name="updated_at" type="TIMESTAMP"/>
                <column name="created_by_id" type="INTEGER" foreign_key="users.id"/>
              </columns>
            </table>
          </data_model>
        </backend_design>
        
        <frontend_design>
          <components>
            <component name="ProjectList">
              <description>Display paginated list of projects</description>
              <features>
                <feature>Search by name, description, client</feature>
                <feature>Sort by various fields</feature>
                <feature>Pagination</feature>
              </features>
            </component>
            <component name="ProjectForm">
              <description>Create/edit project form</description>
              <fields>
                <field name="name" type="text" required="true"/>
                <field name="description" type="textarea"/>
                <field name="client_name" type="text"/>
                <field name="status" type="select" options="draft,active,completed"/>
              </fields>
            </component>
          </components>
        </frontend_design>
      </architecture>
    </design_document>
  </example>
  
  <example name="complex_calculation_service">
    <description>Electrical load calculation service</description>
    <context>
      <user_request>Design electrical load calculation system for residential buildings</user_request>
      <project_context>Need to calculate electrical loads based on NEC standards</project_context>
    </context>
    
    <design_document>
      <executive_summary>
        Design a comprehensive electrical load calculation system that implements NEC (National Electrical Code) standards for residential electrical load calculations.
      </executive_summary>
      
      <requirements>
        <functional_requirements>
          <requirement>Calculate general lighting loads</requirement>
          <requirement>Calculate appliance loads</requirement>
          <requirement>Calculate HVAC loads</requirement>
          <requirement>Apply NEC demand factors</requirement>
          <requirement>Generate load calculation reports</requirement>
        </functional_requirements>
      </requirements>
      
      <architecture>
        <backend_design>
          <service_layer>
            <service name="LoadCalculationService">
              <methods>
                <method name="calculate_general_lighting">Calculate lighting loads per NEC 220.12</method>
                <method name="calculate_appliance_loads">Calculate appliance loads per NEC 220.53</method>
                <method name="calculate_hvac_loads">Calculate HVAC loads per NEC 220.82</method>
                <method name="apply_demand_factors">Apply appropriate demand factors</method>
              </methods>
            </service>
          </service_layer>
          
          <data_models>
            <model name="LoadCalculation">
              <fields>
                <field name="dwelling_area_sqft" type="float"/>
                <field name="small_appliance_circuits" type="int"/>
                <field name="laundry_circuits" type="int"/>
                <field name="fastened_in_place_loads" type="json"/>
                <field name="appliance_loads" type="json"/>
                <field name="hvac_loads" type="json"/>
              </fields>
            </model>
          </data_models>
        </backend_design>
        
        <frontend_design>
          <components>
            <component name="LoadCalculationForm">
              <description>Input form for load calculation parameters</description>
            </component>
            <component name="LoadCalculationResults">
              <description>Display calculated loads and demand factors</description>
            </component>
          </components>
        </frontend_design>
      </architecture>
    </design_document>
  </example>
</design_examples>