<architectural_patterns>
  <backend_patterns>
    <pattern name="crud_endpoint_factory">
      <description>Standard pattern for CRUD operations using the factory</description>
      <when_to_use>
        <condition><PERSON><PERSON><PERSON> has standard CRUD operations</condition>
        <condition>No complex business logic required</condition>
        <condition>Follows standard REST patterns</condition>
      </when_to_use>
      <implementation_guide>
        <step>Identify entity name and plural form</step>
        <step>Create Pydantic schemas:
          <code language="python">
CreateSchema - for POST requests
ReadSchema - for GET responses
UpdateSchema - for PUT/PATCH requests
ListResponseSchema - for paginated responses
          </code>
        </step>
        <step>Define service class extending base service</step>
        <step>Configure factory parameters:
          <parameters>
            <parameter name="entity_name">Singular entity name</parameter>
            <parameter name="entity_name_plural">Plural entity name</parameter>
            <parameter name="create_schema">CreateSchema class</parameter>
            <parameter name="read_schema">ReadSchema class</parameter>
            <parameter name="update_schema">UpdateSchema class</parameter>
            <parameter name="list_response_schema">ListResponseSchema class</parameter>
            <parameter name="service_class">Service implementation</parameter>
            <parameter name="searchable_fields">List of searchable fields</parameter>
            <parameter name="sortable_fields">List of sortable fields</parameter>
          </parameters>
        </step>
      </implementation_guide>
      <example>
        <entity>Project</entity>
        <configuration>
          <code language="python">
crud_router = create_simple_crud_router(
    entity_name="project",
    entity_name_plural="projects",
    create_schema=ProjectCreate,
    read_schema=ProjectRead,
    update_schema=ProjectUpdate,
    list_response_schema=ProjectListResponse,
    service_class=ProjectService,
    service_dependency=get_project_service,
    id_type=int,
    searchable_fields=["name", "description"],
    sortable_fields=["name", "created_at", "updated_at"],
)
          </code>
        </configuration>
      </example>
    </pattern>

    <pattern name="service_layer_pattern">
      <description>Business logic encapsulation in service layer</description>
      <structure>
        <layer name="service">
          <responsibilities>
            <item>Business logic implementation</item>
            <item>Data validation and transformation</item>
            <item>Cross-cutting concerns (caching, logging)</item>
            <item>Transaction management</item>
          </responsibilities>
        </layer>
      </structure>
      <implementation>
        <step>Create service class inheriting from BaseService</step>
        <step>Implement business methods with proper typing</step>
        <step>Add decorators for error handling and monitoring</step>
        <step>Include comprehensive docstrings</step>
      </implementation>
    </pattern>

    <pattern name="repository_pattern">
      <description>Data access abstraction</description>
      <structure>
        <layer name="repository">
          <responsibilities>
            <item>Database query construction</item>
            <item>Data persistence operations</item>
            <item>Complex query optimization</item>
            <item>Database transaction handling</item>
          </responsibilities>
        </layer>
      </structure>
      <implementation>
        <step>Create repository class with SQLAlchemy models</step>
        <step>Implement query methods with proper typing</step>
        <step>Use SQLAlchemy relationships effectively</step>
        <step>Add query optimization hints where needed</step>
      </implementation>
    </pattern>
  </backend_patterns>

  <frontend_patterns>
    <pattern name="component_composition">
      <description>Reusable component architecture</description>
      <principles>
        <principle>Single Responsibility Principle</principle>
        <principle>Composition over inheritance</principle>
        <principle>Props interface consistency</principle>
      </principles>
      <structure>
        <component_type name="atoms">
          <description>Basic building blocks</description>
          <examples>
            <example>Button, Input, Label</example>
          </examples>
        </component_type>
        <component_type name="molecules">
          <description>Simple component groups</description>
          <examples>
            <example>FormField, SearchBar, Card</example>
          </examples>
        </component_type>
        <component_type name="organisms">
          <description>Complex component compositions</description>
          <examples>
            <example>DataTable, Form, Dashboard</example>
          </examples>
        </component_type>
      </structure>
    </pattern>

    <pattern name="state_management">
      <description>Zustand store design</description>
      <structure>
        <store_type name="server_state">
          <description>React Query for server data</description>
          <usage>API calls, caching, synchronization</usage>
        </store_type>
        <store_type name="client_state">
          <description>Zustand for client state</description>
          <usage>UI state, form state, navigation</usage>
        </store_type>
      </structure>
    </pattern>

    <pattern name="api_integration">
      <description>React Query patterns</description>
      <patterns>
        <pattern name="query_hooks">
          <description>Custom hooks for data fetching</description>
          <structure>
            <hook>use[Entity]Query</hook>
            <hook>use[Entity]ListQuery</hook>
            <hook>useCreate[Entity]Mutation</hook>
            <hook>useUpdate[Entity]Mutation</hook>
            <hook>useDelete[Entity]Mutation</hook>
          </structure>
        </pattern>
      </patterns>
    </pattern>
  </frontend_patterns>

  <security_patterns>
    <pattern name="authentication_flow">
      <description>JWT-based authentication design</description>
      <components>
        <component name="token_generation">
          <description>Access and refresh token creation</description>
        </component>
        <component name="token_validation">
          <description>Token verification and refresh</description>
        </component>
        <component name="session_management">
          <description>User session handling</description>
        </component>
      </components>
    </pattern>

    <pattern name="authorization_design">
      <description>Role-based access control</description>
      <structure>
        <level name="api_level">
          <description>Endpoint-level authorization</description>
        </level>
        <level name="service_level">
          <description>Business logic authorization</description>
        </level>
        <level name="data_level">
          <description>Row-level security</description>
        </level>
      </structure>
    </pattern>
  </security_patterns>

  <validation_patterns>
    <pattern name="input_validation">
      <description>Pydantic schema validation</description>
      <approach>
        <step>Define comprehensive Pydantic schemas</step>
        <step>Use Field validators for complex validation</step>
        <step>Include clear error messages</step>
        <step>Document validation rules</step>
      </approach>
    </pattern>

    <pattern name="business_rule_validation">
      <description>Service-level validation</description>
      <approach>
        <step>Implement validation methods in service layer</step>
        <step>Use custom validators for complex rules</step>
        <step>Provide detailed validation errors</step>
        <step>Ensure validation is testable</step>
      </approach>
    </pattern>
  </validation_patterns>
</architectural_patterns>