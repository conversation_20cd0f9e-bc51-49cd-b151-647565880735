"""Dynamic Database Connection Management.

This module provides a robust, context-aware connection and session manager
capable of handling connections to both the central PostgreSQL database and multiple,
project-specific shared local PostgreSQL databases.
"""

from typing import Any, Dict, Optional, AsyncGenerator
from fastapi import Depends
from sqlalchemy.ext.asyncio import (
    create_async_engine,
    AsyncEngine,
    AsyncSession,
    async_sessionmaker,
)

from src.config.logging_config import logger
from src.config.settings import settings
from src.core.repositories.general.project_repository import ProjectRepository
from src.core.errors.exceptions import DatabaseError, ProjectNotFoundError


class DynamicConnectionManager:
    """Manages database connections for central and local databases."""

    def __init__(self) -> None:
        """Initializes the DynamicConnectionManager."""
        self._central_engine: Optional[AsyncEngine] = None
        self._central_session_factory: Optional[async_sessionmaker[AsyncSession]] = None
        self._local_engines: Dict[str, AsyncEngine] = {}
        self._local_session_factories: Dict[str, async_sessionmaker[AsyncSession]] = {}
        logger.info("DynamicConnectionManager initialized.")

    async def initialize(self) -> None:
        """Initializes the central database engine and session factory."""
        if not settings.DATABASE_URL:
            raise DatabaseError(reason="DATABASE_URL is not set.")
        # Central DB setup
        from src.core.database.engine import _create_and_test_async_engine

        self._central_engine = await _create_and_test_async_engine(
            settings.DATABASE_URL,
            echo=settings.DB_ECHO,
        )
        self._central_session_factory = async_sessionmaker(
            bind=self._central_engine,
            class_=AsyncSession,
            expire_on_commit=False,
        )
        logger.info("Central PostgreSQL engine and session factory initialized.")

    async def get_session(
        self, project_repository: ProjectRepository, project_id: Optional[int] = None
    ) -> AsyncGenerator[AsyncSession, None]:
        """
        Provides a database session based on the project context.

        If a project_id is provided, it resolves the appropriate database
        (local or central) based on the project's settings. If no project_id
        is given, it defaults to the central database.

        Args:
            project_repository: An instance of ProjectRepository to fetch project details.
            project_id: The ID of the project to get a session for.

        Yields:
            An AsyncSession for the correct database.
        """
        session_factory = None
        if project_id:
            try:
                project = await project_repository.get_by_id(project_id)
                if project and project.database_url:
                    logger.debug(f"Project {project_id} uses local database.")
                    session_factory = await self._get_local_session_factory(project.database_url)
                else:
                    logger.debug(f"Project {project_id} uses central database.")
                    session_factory = self._central_session_factory
            except ProjectNotFoundError:
                logger.warning(f"Project with ID {project_id} not found. Defaulting to central database.")
                session_factory = self._central_session_factory
        else:
            logger.debug("No project_id provided. Defaulting to central database.")
            session_factory = self._central_session_factory

        if not session_factory:
            raise DatabaseError(reason="Could not determine a valid session factory.")

        async with session_factory() as session:
            yield session

    async def _get_local_session_factory(self, db_url: str) -> async_sessionmaker[AsyncSession]:
        """
        Gets or creates a session factory for a given local database URL.

        Caches engines and session factories by URL to avoid redundant setup.

        Args:
            db_url: The connection URL for the local database.

        Returns:
            An async_sessionmaker for the specified local database.
        """
        if db_url not in self._local_session_factories:
            logger.info(f"Creating new engine and session factory for local DB: {db_url}")
            from src.core.database.engine import _create_and_test_async_engine

            engine = await _create_and_test_async_engine(db_url)
            self._local_engines[db_url] = engine
            self._local_session_factories[db_url] = async_sessionmaker(
                bind=engine,
                class_=AsyncSession,
                expire_on_commit=False,
            )
        return self._local_session_factories[db_url]

    async def shutdown(self) -> None:
        """Disposes of all managed database engines."""
        if self._central_engine:
            await self._central_engine.dispose()
            logger.info("Central database engine disposed.")
        for url, engine in self._local_engines.items():
            await engine.dispose()
            logger.info(f"Local database engine for {url} disposed.")
        self._local_engines.clear()
        self._local_session_factories.clear()


# --- Dependency Injection ---

_connection_manager: "DynamicConnectionManager" = DynamicConnectionManager()


async def initialize_connection_manager() -> None:
    """Global initializer for the connection manager."""
    await _connection_manager.initialize()


async def shutdown_connection_manager() -> None:
    """Global shutdown for the connection manager."""
    await _connection_manager.shutdown()


async def get_central_db_session() -> AsyncGenerator[AsyncSession, None]:
    """Provides a session to the central database."""
    if not _connection_manager._central_session_factory:
        raise DatabaseError(reason="Central session factory is not initialized.")
    async with _connection_manager._central_session_factory() as session:
        yield session


async def get_project_repository_dependency(
    session: AsyncSession = Depends(get_central_db_session),
) -> ProjectRepository:
    """Dependency to get a ProjectRepository instance with a central DB session."""
    return ProjectRepository(session)


async def get_contextual_db_session(
    project_id: Optional[int] = None,
) -> AsyncGenerator[AsyncSession, None]:
    """
    FastAPI dependency that provides a context-aware database session.

    This function creates its own temporary project repository to avoid circular dependencies.
    """
    # Create a temporary session to get project info if needed
    if project_id:
        # Use central session to create a temporary project repository
        async with _connection_manager._central_session_factory() as temp_session:
            temp_project_repo = ProjectRepository(temp_session)
            async for session in _connection_manager.get_session(temp_project_repo, project_id):
                yield session
    else:
        # No project_id, use central database
        async with _connection_manager._central_session_factory() as session:
            yield session


async def get_project_contextual_db_session(
    project_id: int,
    project_repo: ProjectRepository = Depends(get_project_repository_dependency),
) -> AsyncGenerator[AsyncSession, None]:
    """
    FastAPI dependency that provides a project-specific database session.

    This dependency extracts the project_id from the path parameters and returns
    a database session correctly routed to either the central database or the
    project-specific local database.

    Args:
        project_id: The ID of the project (extracted from path parameters)
        project_repo: Repository for project operations (dependency injection)

    Yields:
        AsyncSession: Database session for the correct database
    """
    async for session in _connection_manager.get_session(project_repo, project_id):
        yield session
